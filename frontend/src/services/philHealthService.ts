// OCTAVE Healthcare Platform - PhilHealth Integration Service
// Dedicated service layer for PhilHealth API authentication and operations

import axios, { AxiosResponse, AxiosInstance } from 'axios';
import { ApiResponse } from '@/types';

export interface PhilHealthCredentials {
  readonly facilityCode: string;
  readonly username: string;
  readonly password: string;
  readonly environment: 'sandbox' | 'production';
}

export interface PhilHealthAuthResponse {
  readonly accessToken: string;
  readonly refreshToken: string;
  readonly expiresIn: number;
  readonly tokenType: string;
  readonly scope: string;
}

export interface PhilHealthConnectionStatus {
  readonly isConnected: boolean;
  readonly lastConnected?: Date;
  readonly connectionError?: string;
  readonly facilityCode?: string;
  readonly environment: 'sandbox' | 'production';
}

export interface PhilHealthEligibilityRequest {
  readonly memberPin: string;
  readonly lastName: string;
  readonly firstName: string;
  readonly middleName?: string;
  readonly birthDate: string;
  readonly memberType: 'PRINCIPAL' | 'DEPENDENT';
}

export interface PhilHealthEligibilityResponse {
  readonly isEligible: boolean;
  readonly memberInfo: {
    readonly pin: string;
    readonly lastName: string;
    readonly firstName: string;
    readonly middleName?: string;
    readonly memberType: string;
    readonly membershipType: string;
    readonly effectivityDate: string;
    readonly expiryDate?: string;
  };
  readonly benefits: Array<{
    readonly benefitCode: string;
    readonly benefitName: string;
    readonly availableAmount: number;
    readonly usedAmount: number;
  }>;
}

export interface PhilHealthClaimRequest {
  readonly claimNumber: string;
  readonly memberPin: string;
  readonly facilityCode: string;
  readonly admissionDate: string;
  readonly dischargeDate?: string;
  readonly diagnosis: Array<{
    readonly code: string;
    readonly description: string;
    readonly type: 'PRIMARY' | 'SECONDARY';
  }>;
  readonly procedures: Array<{
    readonly code: string;
    readonly description: string;
    readonly amount: number;
  }>;
}

class PhilHealthService {
  private apiClient: AxiosInstance;
  private authToken: string | null = null;
  private refreshToken: string | null = null;
  private tokenExpiry: Date | null = null;
  private credentials: PhilHealthCredentials | null = null;

  constructor() {
    this.apiClient = axios.create({
      timeout: 30000, // PhilHealth API can be slow
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
      },
    });

    // Request interceptor to add auth token
    this.apiClient.interceptors.request.use(
      (config) => {
        if (this.authToken) {
          config.headers.Authorization = `Bearer ${this.authToken}`;
        }
        return config;
      },
      (error) => Promise.reject(error)
    );

    // Response interceptor for token refresh
    this.apiClient.interceptors.response.use(
      (response) => response,
      async (error) => {
        if (error.response?.status === 401 && this.refreshToken) {
          try {
            await this.refreshAccessToken();
            // Retry the original request
            return this.apiClient.request(error.config);
          } catch (refreshError) {
            // Refresh failed, clear tokens and throw error
            this.clearTokens();
            throw refreshError;
          }
        }
        return Promise.reject(error);
      }
    );

    // Load saved credentials and tokens
    this.loadStoredCredentials();
  }

  /**
   * Set PhilHealth API base URL based on environment
   */
  private setBaseUrl(environment: 'sandbox' | 'production'): void {
    const baseUrls = {
      sandbox: 'https://api-sandbox.philhealth.gov.ph',
      production: 'https://api.philhealth.gov.ph',
    };
    this.apiClient.defaults.baseURL = baseUrls[environment];
  }

  /**
   * Authenticate with PhilHealth API
   */
  async authenticate(credentials: PhilHealthCredentials): Promise<PhilHealthAuthResponse> {
    try {
      this.credentials = credentials;
      this.setBaseUrl(credentials.environment);

      const response: AxiosResponse<PhilHealthAuthResponse> = await this.apiClient.post(
        '/oauth/token',
        {
          grant_type: 'password',
          username: credentials.username,
          password: credentials.password,
          facility_code: credentials.facilityCode,
        }
      );

      const authData = response.data;
      this.authToken = authData.accessToken;
      this.refreshToken = authData.refreshToken;
      this.tokenExpiry = new Date(Date.now() + authData.expiresIn * 1000);

      // Store credentials and tokens securely
      this.storeCredentials();

      return authData;
    } catch (error: any) {
      const errorMessage = error.response?.data?.error_description || 
                          error.response?.data?.message || 
                          error.message || 
                          'PhilHealth authentication failed';
      throw new Error(`PhilHealth Auth Error: ${errorMessage}`);
    }
  }

  /**
   * Refresh access token using refresh token
   */
  private async refreshAccessToken(): Promise<void> {
    if (!this.refreshToken || !this.credentials) {
      throw new Error('No refresh token or credentials available');
    }

    try {
      const response: AxiosResponse<PhilHealthAuthResponse> = await this.apiClient.post(
        '/oauth/token',
        {
          grant_type: 'refresh_token',
          refresh_token: this.refreshToken,
          facility_code: this.credentials.facilityCode,
        }
      );

      const authData = response.data;
      this.authToken = authData.accessToken;
      this.refreshToken = authData.refreshToken;
      this.tokenExpiry = new Date(Date.now() + authData.expiresIn * 1000);

      this.storeCredentials();
    } catch (error: any) {
      throw new Error(`Token refresh failed: ${error.message}`);
    }
  }

  /**
   * Check member eligibility
   */
  async checkEligibility(request: PhilHealthEligibilityRequest): Promise<PhilHealthEligibilityResponse> {
    try {
      await this.ensureAuthenticated();

      const response: AxiosResponse<PhilHealthEligibilityResponse> = await this.apiClient.post(
        '/eligibility/check',
        request
      );

      return response.data;
    } catch (error: any) {
      throw new Error(`Eligibility check failed: ${error.response?.data?.message || error.message}`);
    }
  }

  /**
   * Submit claim to PhilHealth
   */
  async submitClaim(request: PhilHealthClaimRequest): Promise<{ claimId: string; status: string }> {
    try {
      await this.ensureAuthenticated();

      const response: AxiosResponse<{ claimId: string; status: string }> = await this.apiClient.post(
        '/claims/submit',
        request
      );

      return response.data;
    } catch (error: any) {
      throw new Error(`Claim submission failed: ${error.response?.data?.message || error.message}`);
    }
  }

  /**
   * Get connection status
   */
  getConnectionStatus(): PhilHealthConnectionStatus {
    return {
      isConnected: !!this.authToken && !!this.tokenExpiry && this.tokenExpiry > new Date(),
      lastConnected: this.tokenExpiry ? new Date(this.tokenExpiry.getTime() - 3600000) : undefined,
      facilityCode: this.credentials?.facilityCode,
      environment: this.credentials?.environment || 'sandbox',
    };
  }

  /**
   * Disconnect from PhilHealth
   */
  async disconnect(): Promise<void> {
    try {
      if (this.authToken) {
        await this.apiClient.post('/oauth/revoke', {
          token: this.authToken,
        });
      }
    } catch (error) {
      // Continue with disconnect even if revoke fails
      console.warn('PhilHealth token revocation failed:', error);
    } finally {
      this.clearTokens();
    }
  }

  /**
   * Ensure we have a valid authentication token
   */
  private async ensureAuthenticated(): Promise<void> {
    if (!this.authToken || !this.tokenExpiry) {
      throw new Error('Not authenticated with PhilHealth. Please authenticate first.');
    }

    // Check if token is expired (with 5 minute buffer)
    if (this.tokenExpiry.getTime() - Date.now() < 5 * 60 * 1000) {
      if (this.refreshToken) {
        await this.refreshAccessToken();
      } else {
        throw new Error('Authentication token expired. Please re-authenticate.');
      }
    }
  }

  /**
   * Store credentials and tokens securely
   */
  private storeCredentials(): void {
    if (this.credentials) {
      // Store non-sensitive data in localStorage
      localStorage.setItem('philhealth_facility_code', this.credentials.facilityCode);
      localStorage.setItem('philhealth_environment', this.credentials.environment);
      localStorage.setItem('philhealth_username', this.credentials.username);
    }

    if (this.authToken && this.tokenExpiry) {
      // Store tokens securely (consider using secure storage in production)
      sessionStorage.setItem('philhealth_access_token', this.authToken);
      sessionStorage.setItem('philhealth_token_expiry', this.tokenExpiry.toISOString());
      
      if (this.refreshToken) {
        sessionStorage.setItem('philhealth_refresh_token', this.refreshToken);
      }
    }
  }

  /**
   * Load stored credentials and tokens
   */
  private loadStoredCredentials(): void {
    try {
      const facilityCode = localStorage.getItem('philhealth_facility_code');
      const environment = localStorage.getItem('philhealth_environment') as 'sandbox' | 'production';
      const username = localStorage.getItem('philhealth_username');

      if (facilityCode && environment && username) {
        this.credentials = {
          facilityCode,
          environment,
          username,
          password: '', // Password not stored for security
        };
        this.setBaseUrl(environment);
      }

      // Load tokens
      const accessToken = sessionStorage.getItem('philhealth_access_token');
      const tokenExpiry = sessionStorage.getItem('philhealth_token_expiry');
      const refreshToken = sessionStorage.getItem('philhealth_refresh_token');

      if (accessToken && tokenExpiry) {
        this.authToken = accessToken;
        this.tokenExpiry = new Date(tokenExpiry);
        this.refreshToken = refreshToken;
      }
    } catch (error) {
      console.warn('Failed to load stored PhilHealth credentials:', error);
    }
  }

  /**
   * Clear all tokens and credentials
   */
  private clearTokens(): void {
    this.authToken = null;
    this.refreshToken = null;
    this.tokenExpiry = null;
    
    // Clear stored tokens
    sessionStorage.removeItem('philhealth_access_token');
    sessionStorage.removeItem('philhealth_refresh_token');
    sessionStorage.removeItem('philhealth_token_expiry');
  }
}

// Export singleton instance
export const philHealthService = new PhilHealthService();
export default philHealthService;
