import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import { Patient, ApiResponse, PaginatedResponse } from '../../types';

interface PatientSearchParams {
  query?: string;
  mrn?: string;
  firstName?: string;
  lastName?: string;
  dateOfBirth?: string;
  gender?: string;
  phone?: string;
  insurancePolicyNumber?: string;
  limit?: number;
  offset?: number;
}

interface PatientsState {
  // Search functionality
  searchResults: Patient[];
  isSearching: boolean;
  searchError: string | null;
  
  // Current patient
  currentPatient: Patient | null;
  isLoadingPatient: boolean;
  patientError: string | null;
  
  // Patient list
  patients: Patient[];
  totalPatients: number;
  isLoadingPatients: boolean;
  patientsError: string | null;
  
  // Patient registration
  isRegistering: boolean;
  registrationError: string | null;
  
  // Patient updates
  isUpdating: boolean;
  updateError: string | null;
  
  // Merge/deduplication
  duplicatePatients: Patient[];
  isMerging: boolean;
  mergeError: string | null;
}

const initialState: PatientsState = {
  searchResults: [],
  isSearching: false,
  searchError: null,
  
  currentPatient: null,
  isLoadingPatient: false,
  patientError: null,
  
  patients: [],
  totalPatients: 0,
  isLoadingPatients: false,
  patientsError: null,
  
  isRegistering: false,
  registrationError: null,
  
  isUpdating: false,
  updateError: null,
  
  duplicatePatients: [],
  isMerging: false,
  mergeError: null,
};

// Async thunks
export const searchPatients = createAsyncThunk(
  'patients/search',
  async (params: PatientSearchParams, { rejectWithValue }) => {
    try {
      const queryParams = new URLSearchParams();
      
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined && value !== '') {
          queryParams.append(key, value.toString());
        }
      });

      const response = await fetch(`/api/patients/search?${queryParams}`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error('Failed to search patients');
      }

      const data: ApiResponse<Patient[]> = await response.json();
      
      if (!data.success) {
        throw new Error(data.error || 'Search failed');
      }

      return data.data || [];
    } catch (error) {
      return rejectWithValue(error instanceof Error ? error.message : 'Search failed');
    }
  }
);

export const getPatientById = createAsyncThunk(
  'patients/getById',
  async (patientId: string, { rejectWithValue }) => {
    try {
      const response = await fetch(`/api/patients/${patientId}`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error('Failed to fetch patient');
      }

      const data: ApiResponse<Patient> = await response.json();
      
      if (!data.success) {
        throw new Error(data.error || 'Failed to fetch patient');
      }

      return data.data!;
    } catch (error) {
      return rejectWithValue(error instanceof Error ? error.message : 'Failed to fetch patient');
    }
  }
);

export const getPatients = createAsyncThunk(
  'patients/getAll',
  async (params: { page?: number; limit?: number; sortBy?: string; sortOrder?: 'asc' | 'desc' } = {}, { rejectWithValue }) => {
    try {
      const queryParams = new URLSearchParams();
      
      if (params.page) queryParams.append('page', params.page.toString());
      if (params.limit) queryParams.append('limit', params.limit.toString());
      if (params.sortBy) queryParams.append('sortBy', params.sortBy);
      if (params.sortOrder) queryParams.append('sortOrder', params.sortOrder);

      const response = await fetch(`/api/patients?${queryParams}`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error('Failed to fetch patients');
      }

      const data: ApiResponse<PaginatedResponse<Patient>> = await response.json();
      
      if (!data.success) {
        throw new Error(data.error || 'Failed to fetch patients');
      }

      return data.data!;
    } catch (error) {
      return rejectWithValue(error instanceof Error ? error.message : 'Failed to fetch patients');
    }
  }
);

export const registerPatient = createAsyncThunk(
  'patients/register',
  async (patientData: Omit<Patient, 'id' | 'createdAt' | 'updatedAt'>, { rejectWithValue }) => {
    try {
      const response = await fetch('/api/patients', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(patientData),
      });

      if (!response.ok) {
        throw new Error('Failed to register patient');
      }

      const data: ApiResponse<Patient> = await response.json();
      
      if (!data.success) {
        throw new Error(data.error || 'Failed to register patient');
      }

      return data.data!;
    } catch (error) {
      return rejectWithValue(error instanceof Error ? error.message : 'Failed to register patient');
    }
  }
);

export const updatePatient = createAsyncThunk(
  'patients/update',
  async ({ patientId, updates }: { patientId: string; updates: Partial<Patient> }, { rejectWithValue }) => {
    try {
      const response = await fetch(`/api/patients/${patientId}`, {
        method: 'PUT',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(updates),
      });

      if (!response.ok) {
        throw new Error('Failed to update patient');
      }

      const data: ApiResponse<Patient> = await response.json();
      
      if (!data.success) {
        throw new Error(data.error || 'Failed to update patient');
      }

      return data.data!;
    } catch (error) {
      return rejectWithValue(error instanceof Error ? error.message : 'Failed to update patient');
    }
  }
);

export const findDuplicatePatients = createAsyncThunk(
  'patients/findDuplicates',
  async (patientData: Partial<Patient>, { rejectWithValue }) => {
    try {
      const response = await fetch('/api/patients/duplicates', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(patientData),
      });

      if (!response.ok) {
        throw new Error('Failed to find duplicates');
      }

      const data: ApiResponse<Patient[]> = await response.json();
      
      if (!data.success) {
        throw new Error(data.error || 'Failed to find duplicates');
      }

      return data.data || [];
    } catch (error) {
      return rejectWithValue(error instanceof Error ? error.message : 'Failed to find duplicates');
    }
  }
);

export const mergePatients = createAsyncThunk(
  'patients/merge',
  async ({ primaryPatientId, duplicatePatientIds }: { primaryPatientId: string; duplicatePatientIds: string[] }, { rejectWithValue }) => {
    try {
      const response = await fetch('/api/patients/merge', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ primaryPatientId, duplicatePatientIds }),
      });

      if (!response.ok) {
        throw new Error('Failed to merge patients');
      }

      const data: ApiResponse<Patient> = await response.json();
      
      if (!data.success) {
        throw new Error(data.error || 'Failed to merge patients');
      }

      return data.data!;
    } catch (error) {
      return rejectWithValue(error instanceof Error ? error.message : 'Failed to merge patients');
    }
  }
);

// Slice
const patientsSlice = createSlice({
  name: 'patients',
  initialState,
  reducers: {
    clearPatientSearch: (state) => {
      state.searchResults = [];
      state.searchError = null;
    },
    clearCurrentPatient: (state) => {
      state.currentPatient = null;
      state.patientError = null;
    },
    clearErrors: (state) => {
      state.searchError = null;
      state.patientError = null;
      state.patientsError = null;
      state.registrationError = null;
      state.updateError = null;
      state.mergeError = null;
    },
    setCurrentPatient: (state, action: PayloadAction<Patient>) => {
      state.currentPatient = action.payload;
    },
    clearDuplicatePatients: (state) => {
      state.duplicatePatients = [];
      state.mergeError = null;
    },
  },
  extraReducers: (builder) => {
    // Search patients
    builder
      .addCase(searchPatients.pending, (state) => {
        state.isSearching = true;
        state.searchError = null;
      })
      .addCase(searchPatients.fulfilled, (state, action) => {
        state.isSearching = false;
        state.searchResults = action.payload;
        state.searchError = null;
      })
      .addCase(searchPatients.rejected, (state, action) => {
        state.isSearching = false;
        state.searchError = action.payload as string;
        state.searchResults = [];
      });

    // Get patient by ID
    builder
      .addCase(getPatientById.pending, (state) => {
        state.isLoadingPatient = true;
        state.patientError = null;
      })
      .addCase(getPatientById.fulfilled, (state, action) => {
        state.isLoadingPatient = false;
        state.currentPatient = action.payload;
        state.patientError = null;
      })
      .addCase(getPatientById.rejected, (state, action) => {
        state.isLoadingPatient = false;
        state.patientError = action.payload as string;
      });

    // Get patients
    builder
      .addCase(getPatients.pending, (state) => {
        state.isLoadingPatients = true;
        state.patientsError = null;
      })
      .addCase(getPatients.fulfilled, (state, action) => {
        state.isLoadingPatients = false;
        state.patients = action.payload.items;
        state.totalPatients = action.payload.totalCount;
        state.patientsError = null;
      })
      .addCase(getPatients.rejected, (state, action) => {
        state.isLoadingPatients = false;
        state.patientsError = action.payload as string;
      });

    // Register patient
    builder
      .addCase(registerPatient.pending, (state) => {
        state.isRegistering = true;
        state.registrationError = null;
      })
      .addCase(registerPatient.fulfilled, (state, action) => {
        state.isRegistering = false;
        state.currentPatient = action.payload;
        state.patients.unshift(action.payload);
        state.totalPatients += 1;
        state.registrationError = null;
      })
      .addCase(registerPatient.rejected, (state, action) => {
        state.isRegistering = false;
        state.registrationError = action.payload as string;
      });

    // Update patient
    builder
      .addCase(updatePatient.pending, (state) => {
        state.isUpdating = true;
        state.updateError = null;
      })
      .addCase(updatePatient.fulfilled, (state, action) => {
        state.isUpdating = false;
        state.currentPatient = action.payload;
        
        // Update in patients list
        const index = state.patients.findIndex(p => p.id === action.payload.id);
        if (index !== -1) {
          state.patients[index] = action.payload;
        }
        
        // Update in search results
        const searchIndex = state.searchResults.findIndex(p => p.id === action.payload.id);
        if (searchIndex !== -1) {
          state.searchResults[searchIndex] = action.payload;
        }
        
        state.updateError = null;
      })
      .addCase(updatePatient.rejected, (state, action) => {
        state.isUpdating = false;
        state.updateError = action.payload as string;
      });

    // Find duplicates
    builder
      .addCase(findDuplicatePatients.fulfilled, (state, action) => {
        state.duplicatePatients = action.payload;
      });

    // Merge patients
    builder
      .addCase(mergePatients.pending, (state) => {
        state.isMerging = true;
        state.mergeError = null;
      })
      .addCase(mergePatients.fulfilled, (state, action) => {
        state.isMerging = false;
        state.currentPatient = action.payload;
        state.duplicatePatients = [];
        state.mergeError = null;
      })
      .addCase(mergePatients.rejected, (state, action) => {
        state.isMerging = false;
        state.mergeError = action.payload as string;
      });
  },
});

export const {
  clearPatientSearch,
  clearCurrentPatient,
  clearErrors,
  setCurrentPatient,
  clearDuplicatePatients,
} = patientsSlice.actions;

export default patientsSlice.reducer;
