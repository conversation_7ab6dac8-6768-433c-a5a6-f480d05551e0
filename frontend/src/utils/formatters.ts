/**
 * Utility functions for formatting data in healthcare applications
 * HIPAA-compliant formatting with PHI protection capabilities
 */

/**
 * Format phone number to standard US format
 */
export const formatPhoneNumber = (phone: string): string => {
  if (!phone) return '';
  
  // Remove all non-digit characters
  const digits = phone.replace(/\D/g, '');
  
  // Format based on length
  if (digits.length === 10) {
    return `(${digits.slice(0, 3)}) ${digits.slice(3, 6)}-${digits.slice(6)}`;
  } else if (digits.length === 11 && digits[0] === '1') {
    return `+1 (${digits.slice(1, 4)}) ${digits.slice(4, 7)}-${digits.slice(7)}`;
  }
  
  return phone; // Return original if can't format
};

/**
 * Calculate age from date of birth
 */
export const calculateAge = (dateOfBirth: Date | string): number => {
  const today = new Date();
  const birthDate = new Date(dateOfBirth);
  let age = today.getFullYear() - birthDate.getFullYear();
  const monthDiff = today.getMonth() - birthDate.getMonth();
  
  if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
    age--;
  }
  
  return age;
};

/**
 * Format date to readable string
 */
export const formatDate = (date: Date | string, options?: Intl.DateTimeFormatOptions): string => {
  if (!date) return '';
  
  const dateObj = new Date(date);
  
  if (isNaN(dateObj.getTime())) {
    return 'Invalid Date';
  }
  
  const defaultOptions: Intl.DateTimeFormatOptions = {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
  };
  
  return dateObj.toLocaleDateString('en-US', options || defaultOptions);
};

/**
 * Format date and time to readable string
 */
export const formatDateTime = (date: Date | string, options?: Intl.DateTimeFormatOptions): string => {
  if (!date) return '';
  
  const dateObj = new Date(date);
  
  if (isNaN(dateObj.getTime())) {
    return 'Invalid Date';
  }
  
  const defaultOptions: Intl.DateTimeFormatOptions = {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
  };
  
  return dateObj.toLocaleDateString('en-US', options || defaultOptions);
};

/**
 * Format currency values
 */
export const formatCurrency = (amount: number, currency: string = 'USD'): string => {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency,
  }).format(amount);
};

/**
 * Format medical record number with standard formatting
 */
export const formatMRN = (mrn: string): string => {
  if (!mrn) return '';
  
  // Remove any existing formatting
  const digits = mrn.replace(/\D/g, '');
  
  // Format as XXX-XXX-XXXX if 10 digits, otherwise return as-is
  if (digits.length === 10) {
    return `${digits.slice(0, 3)}-${digits.slice(3, 6)}-${digits.slice(6)}`;
  }
  
  return mrn;
};

/**
 * Format insurance policy number
 */
export const formatPolicyNumber = (policyNumber: string): string => {
  if (!policyNumber) return '';
  
  // Basic formatting - can be customized per insurance company
  return policyNumber.toUpperCase();
};

/**
 * Format time duration in human-readable format
 */
export const formatDuration = (milliseconds: number): string => {
  const seconds = Math.floor(milliseconds / 1000);
  const minutes = Math.floor(seconds / 60);
  const hours = Math.floor(minutes / 60);
  const days = Math.floor(hours / 24);
  
  if (days > 0) {
    return `${days} day${days !== 1 ? 's' : ''}`;
  } else if (hours > 0) {
    return `${hours} hour${hours !== 1 ? 's' : ''}`;
  } else if (minutes > 0) {
    return `${minutes} minute${minutes !== 1 ? 's' : ''}`;
  } else {
    return `${seconds} second${seconds !== 1 ? 's' : ''}`;
  }
};

/**
 * Format file size in human-readable format
 */
export const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 Bytes';
  
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

/**
 * Mask sensitive data for HIPAA compliance
 */
export const maskPHI = (data: string, type: 'ssn' | 'phone' | 'email' | 'mrn' | 'name' = 'name'): string => {
  if (!data) return '';
  
  switch (type) {
    case 'ssn':
      return data.replace(/(\d{3})-?(\d{2})-?(\d{4})/, 'XXX-XX-$3');
    case 'phone':
      return data.replace(/(\d{3})\d{3}(\d{4})/, '$1-XXX-$2');
    case 'email':
      const [username, domain] = data.split('@');
      if (username && domain) {
        return `${username.slice(0, 2)}***@${domain}`;
      }
      return '<EMAIL>';
    case 'mrn':
      return `***${data.slice(-3)}`;
    case 'name':
      return data.split(' ').map(part => 
        part.length > 1 ? part.charAt(0) + '*'.repeat(part.length - 1) : part
      ).join(' ');
    default:
      return '***';
  }
};

/**
 * Format percentage values
 */
export const formatPercentage = (value: number, decimals: number = 1): string => {
  return `${(value * 100).toFixed(decimals)}%`;
};

/**
 * Format medical codes (ICD-10, CPT, etc.)
 */
export const formatMedicalCode = (code: string, type: 'icd10' | 'cpt' | 'hcpcs' = 'icd10'): string => {
  if (!code) return '';
  
  switch (type) {
    case 'icd10':
      // ICD-10 format: A00.0
      const icd10Clean = code.replace(/[^A-Z0-9]/g, '');
      if (icd10Clean.length >= 4) {
        return `${icd10Clean.slice(0, 3)}.${icd10Clean.slice(3)}`;
      }
      return code;
    case 'cpt':
      // CPT format: 99213
      return code.replace(/[^0-9]/g, '');
    case 'hcpcs':
      // HCPCS format: A0001
      return code.toUpperCase().replace(/[^A-Z0-9]/g, '');
    default:
      return code;
  }
};

/**
 * Format priority levels with appropriate styling
 */
export const formatPriority = (priority: string): { text: string; color: string } => {
  switch (priority.toLowerCase()) {
    case 'stat':
      return { text: 'STAT', color: 'error' };
    case 'urgent':
      return { text: 'Urgent', color: 'warning' };
    case 'routine':
      return { text: 'Routine', color: 'success' };
    default:
      return { text: priority, color: 'default' };
  }
};

/**
 * Format status with appropriate styling
 */
export const formatStatus = (status: string): { text: string; color: string } => {
  switch (status.toLowerCase()) {
    case 'approved':
      return { text: 'Approved', color: 'success' };
    case 'denied':
      return { text: 'Denied', color: 'error' };
    case 'pending':
    case 'submitted':
    case 'under_review':
      return { text: 'Pending', color: 'warning' };
    case 'expired':
      return { text: 'Expired', color: 'error' };
    case 'cancelled':
      return { text: 'Cancelled', color: 'default' };
    case 'draft':
      return { text: 'Draft', color: 'info' };
    default:
      return { text: status, color: 'default' };
  }
};

/**
 * Format relative time (e.g., "2 hours ago")
 */
export const formatRelativeTime = (date: Date | string): string => {
  if (!date) return '';
  
  const dateObj = new Date(date);
  const now = new Date();
  const diffMs = now.getTime() - dateObj.getTime();
  
  const diffSeconds = Math.floor(diffMs / 1000);
  const diffMinutes = Math.floor(diffSeconds / 60);
  const diffHours = Math.floor(diffMinutes / 60);
  const diffDays = Math.floor(diffHours / 24);
  
  if (diffDays > 0) {
    return `${diffDays} day${diffDays !== 1 ? 's' : ''} ago`;
  } else if (diffHours > 0) {
    return `${diffHours} hour${diffHours !== 1 ? 's' : ''} ago`;
  } else if (diffMinutes > 0) {
    return `${diffMinutes} minute${diffMinutes !== 1 ? 's' : ''} ago`;
  } else {
    return 'Just now';
  }
};

/**
 * Validate and format ZIP code
 */
export const formatZipCode = (zipCode: string): string => {
  if (!zipCode) return '';
  
  const digits = zipCode.replace(/\D/g, '');
  
  if (digits.length === 5) {
    return digits;
  } else if (digits.length === 9) {
    return `${digits.slice(0, 5)}-${digits.slice(5)}`;
  }
  
  return zipCode;
};

/**
 * Format address for display
 */
export const formatAddress = (address: {
  street?: string;
  city?: string;
  state?: string;
  zipCode?: string;
  country?: string;
}): string => {
  const parts = [];
  
  if (address.street) parts.push(address.street);
  
  const cityStateZip = [address.city, address.state, address.zipCode]
    .filter(Boolean)
    .join(' ');
  
  if (cityStateZip) parts.push(cityStateZip);
  
  if (address.country && address.country !== 'US') {
    parts.push(address.country);
  }
  
  return parts.join(', ');
};
