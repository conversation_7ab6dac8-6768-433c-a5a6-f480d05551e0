// OCTAVE Healthcare Platform - Healthcare Accessibility Utilities
// WCAG 2.1 AA+ compliance with healthcare-specific accessibility patterns

export interface AccessibilityConfig {
  highContrast: boolean;
  largeText: boolean;
  reducedMotion: boolean;
  screenReaderOptimized: boolean;
  keyboardNavigation: boolean;
  voiceNavigation: boolean;
  colorBlindFriendly: boolean;
}

export interface HealthcareAccessibilityContext {
  userRole: string;
  workEnvironment: 'clinical' | 'administrative' | 'emergency' | 'mobile';
  assistiveTechnology: string[];
  medicalTerminologyLevel: 'basic' | 'intermediate' | 'advanced';
}

// Healthcare-specific ARIA labels and descriptions
export const HealthcareAriaLabels = {
  // Patient information
  patientId: 'Patient identification number',
  medicalRecordNumber: 'Medical record number for patient identification',
  patientName: 'Patient full name',
  dateOfBirth: 'Patient date of birth',
  
  // Medical data
  diagnosis: 'Primary medical diagnosis',
  procedure: 'Medical procedure or treatment',
  medication: 'Prescribed medication',
  dosage: 'Medication dosage and frequency',
  allergies: 'Known patient allergies and reactions',
  
  // Prior authorization
  authorizationStatus: 'Prior authorization approval status',
  authorizationNumber: 'Prior authorization reference number',
  insuranceProvider: 'Patient insurance provider',
  
  // Emergency indicators
  criticalAlert: 'Critical medical alert requiring immediate attention',
  emergencyContact: 'Emergency contact information',
  
  // Navigation
  patientSearch: 'Search for patients by name or ID',
  newPatientForm: 'Create new patient record',
  medicalHistory: 'View patient medical history',
  
  // Actions
  saveRecord: 'Save patient medical record',
  submitAuthorization: 'Submit prior authorization request',
  emergencyCall: 'Initiate emergency call',
} as const;

// Healthcare-specific keyboard shortcuts
export const HealthcareKeyboardShortcuts = {
  // Quick navigation
  'Alt+P': 'Go to patients list',
  'Alt+A': 'Go to prior authorizations',
  'Alt+M': 'Go to messages',
  'Alt+D': 'Go to dashboard',
  
  // Quick actions
  'Ctrl+N': 'Create new patient',
  'Ctrl+S': 'Save current form',
  'Ctrl+F': 'Search patients',
  'Escape': 'Cancel current action',
  
  // Emergency
  'F1': 'Emergency help',
  'F2': 'Emergency contact',
  
  // Accessibility
  'Alt+H': 'Toggle high contrast',
  'Alt+T': 'Toggle large text',
  'Alt+R': 'Toggle reduced motion',
} as const;

// Color schemes for different accessibility needs
export const AccessibilityColorSchemes = {
  default: {
    primary: '#1976d2',
    secondary: '#dc004e',
    success: '#2e7d32',
    warning: '#ed6c02',
    error: '#d32f2f',
    info: '#0288d1',
  },
  highContrast: {
    primary: '#000000',
    secondary: '#ffffff',
    success: '#00ff00',
    warning: '#ffff00',
    error: '#ff0000',
    info: '#0000ff',
  },
  colorBlindFriendly: {
    primary: '#0173b2', // Blue
    secondary: '#de8f05', // Orange
    success: '#029e73', // Green
    warning: '#cc78bc', // Pink
    error: '#ca5010', // Red-orange
    info: '#fbafe4', // Light pink
  },
} as const;

// Screen reader optimizations for medical terminology
export const MedicalTerminologyReader = {
  // Common medical abbreviations with pronunciations
  abbreviations: {
    'BP': 'blood pressure',
    'HR': 'heart rate',
    'RR': 'respiratory rate',
    'O2': 'oxygen',
    'CO2': 'carbon dioxide',
    'IV': 'intravenous',
    'IM': 'intramuscular',
    'PO': 'by mouth',
    'PRN': 'as needed',
    'BID': 'twice daily',
    'TID': 'three times daily',
    'QID': 'four times daily',
    'mg': 'milligrams',
    'ml': 'milliliters',
    'cc': 'cubic centimeters',
  },
  
  // Medical units with proper pronunciation
  units: {
    'mmHg': 'millimeters of mercury',
    'bpm': 'beats per minute',
    'kg': 'kilograms',
    'lbs': 'pounds',
    'cm': 'centimeters',
    'in': 'inches',
    '°F': 'degrees Fahrenheit',
    '°C': 'degrees Celsius',
  },
} as const;

// Accessibility utility functions
export class HealthcareAccessibilityUtils {
  private static config: AccessibilityConfig = {
    highContrast: false,
    largeText: false,
    reducedMotion: false,
    screenReaderOptimized: false,
    keyboardNavigation: true,
    voiceNavigation: false,
    colorBlindFriendly: false,
  };

  static setConfig(newConfig: Partial<AccessibilityConfig>): void {
    this.config = { ...this.config, ...newConfig };
    this.applyAccessibilitySettings();
  }

  static getConfig(): AccessibilityConfig {
    return { ...this.config };
  }

  private static applyAccessibilitySettings(): void {
    const root = document.documentElement;

    // High contrast mode
    if (this.config.highContrast) {
      root.classList.add('high-contrast');
    } else {
      root.classList.remove('high-contrast');
    }

    // Large text mode
    if (this.config.largeText) {
      root.classList.add('large-text');
    } else {
      root.classList.remove('large-text');
    }

    // Reduced motion
    if (this.config.reducedMotion) {
      root.classList.add('reduced-motion');
    } else {
      root.classList.remove('reduced-motion');
    }

    // Color blind friendly
    if (this.config.colorBlindFriendly) {
      root.classList.add('color-blind-friendly');
    } else {
      root.classList.remove('color-blind-friendly');
    }
  }

  static announceToScreenReader(message: string, priority: 'polite' | 'assertive' = 'polite'): void {
    const announcement = document.createElement('div');
    announcement.setAttribute('aria-live', priority);
    announcement.setAttribute('aria-atomic', 'true');
    announcement.className = 'sr-only';
    announcement.textContent = message;
    
    document.body.appendChild(announcement);
    
    // Remove after announcement
    setTimeout(() => {
      document.body.removeChild(announcement);
    }, 1000);
  }

  static expandMedicalAbbreviation(text: string): string {
    let expandedText = text;
    
    // Expand abbreviations
    Object.entries(MedicalTerminologyReader.abbreviations).forEach(([abbr, expansion]) => {
      const regex = new RegExp(`\\b${abbr}\\b`, 'gi');
      expandedText = expandedText.replace(regex, expansion);
    });

    // Expand units
    Object.entries(MedicalTerminologyReader.units).forEach(([unit, expansion]) => {
      const regex = new RegExp(`\\b${unit}\\b`, 'gi');
      expandedText = expandedText.replace(regex, expansion);
    });

    return expandedText;
  }

  static setupKeyboardNavigation(): void {
    document.addEventListener('keydown', (event) => {
      const shortcut = this.getKeyboardShortcut(event);
      if (shortcut && HealthcareKeyboardShortcuts[shortcut]) {
        event.preventDefault();
        this.handleKeyboardShortcut(shortcut);
      }
    });
  }

  private static getKeyboardShortcut(event: KeyboardEvent): string | null {
    const modifiers = [];
    if (event.ctrlKey) modifiers.push('Ctrl');
    if (event.altKey) modifiers.push('Alt');
    if (event.shiftKey) modifiers.push('Shift');
    
    const key = event.key === ' ' ? 'Space' : event.key;
    
    if (modifiers.length > 0) {
      return `${modifiers.join('+')}+${key}`;
    }
    
    return key;
  }

  private static handleKeyboardShortcut(shortcut: string): void {
    // This would be implemented based on the application's routing and state management
    console.log(`Healthcare keyboard shortcut activated: ${shortcut}`);
    
    // Example implementations:
    switch (shortcut) {
      case 'Alt+H':
        this.setConfig({ highContrast: !this.config.highContrast });
        this.announceToScreenReader(
          `High contrast mode ${this.config.highContrast ? 'enabled' : 'disabled'}`
        );
        break;
      case 'Alt+T':
        this.setConfig({ largeText: !this.config.largeText });
        this.announceToScreenReader(
          `Large text mode ${this.config.largeText ? 'enabled' : 'disabled'}`
        );
        break;
      case 'Alt+R':
        this.setConfig({ reducedMotion: !this.config.reducedMotion });
        this.announceToScreenReader(
          `Reduced motion mode ${this.config.reducedMotion ? 'enabled' : 'disabled'}`
        );
        break;
      case 'F1':
        this.announceToScreenReader('Emergency help activated', 'assertive');
        break;
    }
  }

  static validateAccessibility(element: HTMLElement): string[] {
    const issues: string[] = [];

    // Check for missing alt text on images
    const images = element.querySelectorAll('img');
    images.forEach((img) => {
      if (!img.alt && !img.getAttribute('aria-label')) {
        issues.push('Image missing alt text or aria-label');
      }
    });

    // Check for missing labels on form controls
    const formControls = element.querySelectorAll('input, select, textarea');
    formControls.forEach((control) => {
      const hasLabel = control.getAttribute('aria-label') || 
                      control.getAttribute('aria-labelledby') ||
                      element.querySelector(`label[for="${control.id}"]`);
      if (!hasLabel) {
        issues.push('Form control missing label');
      }
    });

    // Check for sufficient color contrast (simplified check)
    const buttons = element.querySelectorAll('button');
    buttons.forEach((button) => {
      const styles = window.getComputedStyle(button);
      const bgColor = styles.backgroundColor;
      const textColor = styles.color;
      
      // This is a simplified check - in practice, you'd use a proper contrast ratio calculator
      if (bgColor === textColor) {
        issues.push('Insufficient color contrast on button');
      }
    });

    return issues;
  }
}

// Initialize accessibility utilities
export const initializeHealthcareAccessibility = (): void => {
  // Load saved accessibility preferences
  const savedConfig = localStorage.getItem('healthcare-accessibility-config');
  if (savedConfig) {
    try {
      const config = JSON.parse(savedConfig);
      HealthcareAccessibilityUtils.setConfig(config);
    } catch (error) {
      console.warn('Failed to load accessibility config:', error);
    }
  }

  // Set up keyboard navigation
  HealthcareAccessibilityUtils.setupKeyboardNavigation();

  // Detect user preferences
  if (window.matchMedia('(prefers-reduced-motion: reduce)').matches) {
    HealthcareAccessibilityUtils.setConfig({ reducedMotion: true });
  }

  if (window.matchMedia('(prefers-contrast: high)').matches) {
    HealthcareAccessibilityUtils.setConfig({ highContrast: true });
  }
};

export default HealthcareAccessibilityUtils;
