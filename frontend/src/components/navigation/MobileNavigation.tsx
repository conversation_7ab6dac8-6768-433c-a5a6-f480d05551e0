// OCTAVE Healthcare Platform - Mobile Navigation Component
// Progressive disclosure patterns for complex healthcare workflows on mobile

import React, { useState, useEffect } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import {
  Box,
  BottomNavigation,
  BottomNavigationAction,
  Drawer,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  ListItemButton,
  Collapse,
  IconButton,
  Typography,
  Badge,
  Fab,
  SpeedDial,
  SpeedDialAction,
  SpeedDialIcon,
  LinearProgress,
  Button,
  useTheme,
  useMediaQuery,
} from '@mui/material';
import {
  HomeRounded,
  PeopleRounded,
  AssignmentRounded,
  MessageRounded,
  MenuRounded,
  CloseRounded,
  ExpandLessRounded,
  ExpandMoreRounded,
  AddRounded,
  SearchRounded,
  NotificationsRounded,
  SettingsRounded,
  PersonAddRounded,
  DescriptionRounded,
  CalendarTodayRounded,
  PhoneRounded,
} from '@mui/icons-material';

interface MobileNavigationProps {
  unreadMessages?: number;
  pendingAuthorizations?: number;
  onQuickAction?: (action: string) => void;
}

interface NavigationItem {
  id: string;
  label: string;
  icon: React.ReactNode;
  path: string;
  badge?: number;
  children?: NavigationItem[];
}

const primaryNavItems: NavigationItem[] = [
  {
    id: 'dashboard',
    label: 'Dashboard',
    icon: <HomeRounded />,
    path: '/dashboard',
  },
  {
    id: 'patients',
    label: 'Patients',
    icon: <PeopleRounded />,
    path: '/patients',
  },
  {
    id: 'authorizations',
    label: 'Prior Auth',
    icon: <AssignmentRounded />,
    path: '/prior-authorizations',
  },
  {
    id: 'messages',
    label: 'Messages',
    icon: <MessageRounded />,
    path: '/messages',
  },
];

const secondaryNavItems: NavigationItem[] = [
  {
    id: 'patients-section',
    label: 'Patient Management',
    icon: <PeopleRounded />,
    path: '/patients',
    children: [
      { id: 'patient-list', label: 'Patient List', icon: <PeopleRounded />, path: '/patients' },
      { id: 'new-patient', label: 'Add Patient', icon: <PersonAddRounded />, path: '/patients/new' },
      { id: 'patient-search', label: 'Search Patients', icon: <SearchRounded />, path: '/patients/search' },
    ],
  },
  {
    id: 'auth-section',
    label: 'Prior Authorizations',
    icon: <AssignmentRounded />,
    path: '/prior-authorizations',
    children: [
      { id: 'auth-list', label: 'All Authorizations', icon: <AssignmentRounded />, path: '/prior-authorizations' },
      { id: 'new-auth', label: 'New Authorization', icon: <AddRounded />, path: '/prior-authorizations/new' },
      { id: 'pending-auth', label: 'Pending Review', icon: <CalendarTodayRounded />, path: '/prior-authorizations/pending' },
    ],
  },
  {
    id: 'records-section',
    label: 'Medical Records',
    icon: <DescriptionRounded />,
    path: '/medical-records',
    children: [
      { id: 'records-list', label: 'All Records', icon: <DescriptionRounded />, path: '/medical-records' },
      { id: 'recent-records', label: 'Recent Records', icon: <CalendarTodayRounded />, path: '/medical-records/recent' },
    ],
  },
];

const quickActions = [
  { id: 'new-patient', label: 'New Patient', icon: <PersonAddRounded /> },
  { id: 'new-auth', label: 'Prior Auth', icon: <AssignmentRounded /> },
  { id: 'emergency', label: 'Emergency', icon: <PhoneRounded /> },
  { id: 'search', label: 'Search', icon: <SearchRounded /> },
];

export const MobileNavigation: React.FC<MobileNavigationProps> = ({
  unreadMessages = 0,
  pendingAuthorizations = 0,
  onQuickAction,
}) => {
  const theme = useTheme();
  const navigate = useNavigate();
  const location = useLocation();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));

  const [drawerOpen, setDrawerOpen] = useState(false);
  const [expandedSections, setExpandedSections] = useState<string[]>([]);
  const [speedDialOpen, setSpeedDialOpen] = useState(false);

  // Get current active tab
  const getCurrentTab = () => {
    const path = location.pathname;
    if (path.includes('/patients')) return 1;
    if (path.includes('/prior-authorizations')) return 2;
    if (path.includes('/messages')) return 3;
    return 0; // dashboard
  };

  const handleBottomNavChange = (event: React.SyntheticEvent, newValue: number) => {
    const item = primaryNavItems[newValue];
    if (item) {
      navigate(item.path);
    }
  };

  const handleDrawerToggle = () => {
    setDrawerOpen(!drawerOpen);
  };

  const handleSectionToggle = (sectionId: string) => {
    setExpandedSections(prev => 
      prev.includes(sectionId) 
        ? prev.filter(id => id !== sectionId)
        : [...prev, sectionId]
    );
  };

  const handleQuickAction = (actionId: string) => {
    setSpeedDialOpen(false);
    onQuickAction?.(actionId);
  };

  const renderNavigationItem = (item: NavigationItem, isChild = false) => {
    const hasChildren = item.children && item.children.length > 0;
    const isExpanded = expandedSections.includes(item.id);

    return (
      <React.Fragment key={item.id}>
        <ListItem disablePadding>
          <ListItemButton
            onClick={() => {
              if (hasChildren) {
                handleSectionToggle(item.id);
              } else {
                navigate(item.path);
                setDrawerOpen(false);
              }
            }}
            sx={{
              pl: isChild ? 4 : 2,
              minHeight: 48,
            }}
          >
            <ListItemIcon sx={{ minWidth: 40 }}>
              {item.badge ? (
                <Badge badgeContent={item.badge} color="error">
                  {item.icon}
                </Badge>
              ) : (
                item.icon
              )}
            </ListItemIcon>
            <ListItemText 
              primary={item.label}
              primaryTypographyProps={{
                variant: isChild ? 'body2' : 'body1',
                fontWeight: isChild ? 'normal' : 'medium',
              }}
            />
            {hasChildren && (
              isExpanded ? <ExpandLessRounded /> : <ExpandMoreRounded />
            )}
          </ListItemButton>
        </ListItem>

        {hasChildren && (
          <Collapse in={isExpanded} timeout="auto" unmountOnExit>
            <List component="div" disablePadding>
              {item.children!.map(child => renderNavigationItem(child, true))}
            </List>
          </Collapse>
        )}
      </React.Fragment>
    );
  };

  if (!isMobile) {
    return null; // Only show on mobile devices
  }

  return (
    <>
      {/* Bottom Navigation */}
      <BottomNavigation
        value={getCurrentTab()}
        onChange={handleBottomNavChange}
        showLabels
        sx={{
          position: 'fixed',
          bottom: 0,
          left: 0,
          right: 0,
          zIndex: theme.zIndex.appBar,
          borderTop: `1px solid ${theme.palette.divider}`,
          backgroundColor: theme.palette.background.paper,
        }}
      >
        <BottomNavigationAction
          label="Dashboard"
          icon={<HomeRounded />}
        />
        <BottomNavigationAction
          label="Patients"
          icon={<PeopleRounded />}
        />
        <BottomNavigationAction
          label="Prior Auth"
          icon={
            pendingAuthorizations > 0 ? (
              <Badge badgeContent={pendingAuthorizations} color="error">
                <AssignmentRounded />
              </Badge>
            ) : (
              <AssignmentRounded />
            )
          }
        />
        <BottomNavigationAction
          label="Messages"
          icon={
            unreadMessages > 0 ? (
              <Badge badgeContent={unreadMessages} color="error">
                <MessageRounded />
              </Badge>
            ) : (
              <MessageRounded />
            )
          }
        />
      </BottomNavigation>

      {/* Menu Button */}
      <Fab
        color="primary"
        size="medium"
        onClick={handleDrawerToggle}
        sx={{
          position: 'fixed',
          top: 16,
          left: 16,
          zIndex: theme.zIndex.appBar + 1,
        }}
      >
        <MenuRounded />
      </Fab>

      {/* Quick Actions Speed Dial */}
      <SpeedDial
        ariaLabel="Quick Actions"
        icon={<SpeedDialIcon />}
        onClose={() => setSpeedDialOpen(false)}
        onOpen={() => setSpeedDialOpen(true)}
        open={speedDialOpen}
        sx={{
          position: 'fixed',
          bottom: 80, // Above bottom navigation
          right: 16,
          zIndex: theme.zIndex.appBar,
        }}
      >
        {quickActions.map((action) => (
          <SpeedDialAction
            key={action.id}
            icon={action.icon}
            tooltipTitle={action.label}
            onClick={() => handleQuickAction(action.id)}
          />
        ))}
      </SpeedDial>

      {/* Navigation Drawer */}
      <Drawer
        anchor="left"
        open={drawerOpen}
        onClose={handleDrawerToggle}
        ModalProps={{
          keepMounted: true, // Better open performance on mobile
        }}
        sx={{
          '& .MuiDrawer-paper': {
            width: 280,
            boxSizing: 'border-box',
          },
        }}
      >
        <Box sx={{ overflow: 'auto' }}>
          {/* Header */}
          <Box sx={{ 
            display: 'flex', 
            alignItems: 'center', 
            justifyContent: 'space-between',
            p: 2,
            borderBottom: `1px solid ${theme.palette.divider}`,
          }}>
            <Typography variant="h6" component="div">
              OCTAVE Healthcare
            </Typography>
            <IconButton onClick={handleDrawerToggle}>
              <CloseRounded />
            </IconButton>
          </Box>

          {/* Navigation Items */}
          <List>
            {secondaryNavItems.map(item => renderNavigationItem(item))}
          </List>

          {/* Settings and Notifications */}
          <Box sx={{ mt: 'auto', borderTop: `1px solid ${theme.palette.divider}` }}>
            <List>
              <ListItem disablePadding>
                <ListItemButton onClick={() => navigate('/notifications')}>
                  <ListItemIcon>
                    <Badge badgeContent={unreadMessages + pendingAuthorizations} color="error">
                      <NotificationsRounded />
                    </Badge>
                  </ListItemIcon>
                  <ListItemText primary="Notifications" />
                </ListItemButton>
              </ListItem>
              <ListItem disablePadding>
                <ListItemButton onClick={() => navigate('/settings')}>
                  <ListItemIcon>
                    <SettingsRounded />
                  </ListItemIcon>
                  <ListItemText primary="Settings" />
                </ListItemButton>
              </ListItem>
            </List>
          </Box>
        </Box>
      </Drawer>

      {/* Spacer for bottom navigation */}
      <Box sx={{ height: 56 }} />
    </>
  );
};

// Mobile-optimized workflow component for progressive disclosure
export const MobileWorkflowStepper: React.FC<{
  steps: Array<{ label: string; content: React.ReactNode }>;
  activeStep: number;
  onStepChange: (step: number) => void;
  onComplete?: () => void;
}> = ({ steps, activeStep, onStepChange, onComplete }) => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));

  if (!isMobile) return null;

  return (
    <Box sx={{ width: '100%', p: 2 }}>
      {/* Progress indicator */}
      <Box sx={{ mb: 2 }}>
        <Typography variant="caption" color="text.secondary">
          Step {activeStep + 1} of {steps.length}
        </Typography>
        <LinearProgress
          variant="determinate"
          value={(activeStep / (steps.length - 1)) * 100}
          sx={{ mt: 1 }}
        />
      </Box>

      {/* Current step content */}
      <Box sx={{ mb: 3 }}>
        <Typography variant="h6" gutterBottom>
          {steps[activeStep]?.label}
        </Typography>
        {steps[activeStep]?.content}
      </Box>

      {/* Navigation buttons */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
        <Button
          disabled={activeStep === 0}
          onClick={() => onStepChange(activeStep - 1)}
          variant="outlined"
        >
          Previous
        </Button>

        {activeStep === steps.length - 1 ? (
          <Button onClick={onComplete} variant="contained">
            Complete
          </Button>
        ) : (
          <Button
            onClick={() => onStepChange(activeStep + 1)}
            variant="contained"
          >
            Next
          </Button>
        )}
      </Box>
    </Box>
  );
};

export default MobileNavigation;
