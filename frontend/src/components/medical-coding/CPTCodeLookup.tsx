import React, { useState, useEffect, useCallback } from 'react';
import {
  <PERSON>,
  Card,
  CardContent,
  Typo<PERSON>,
  TextField,
  Autocomplete,
  Chip,
  List,
  ListItem,
  ListItemText,
  ListItemButton,
  Divider,
  Alert,
  CircularProgress,
  InputAdornment,
  IconButton,
  Tooltip,
  Badge,
  Collapse,
  Grid,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
} from '@mui/material';
import {
  Search as SearchIcon,
  Clear as ClearIcon,
  Star as StarIcon,
  StarBorder as StarBorderIcon,
  History as HistoryIcon,
  ExpandMore as ExpandMoreIcon,
  ExpandLess as ExpandLessIcon,
  Info as InfoIcon,
  CheckCircle as CheckCircleIcon,
  Warning as WarningIcon,
  LocalHospital as MedicalIcon,
  AttachMoney as MoneyIcon,
  Security as SecurityIcon,
} from '@mui/icons-material';

interface CPTCode {
  code: string;
  description: string;
  category: string;
  subcategory?: string;
  modifiers?: CPTModifier[];
  rvu?: {
    work: number;
    practice: number;
    malpractice: number;
    total: number;
  };
  bundlingRules?: string[];
  insuranceCoverage?: InsuranceCoverage[];
  isValid: boolean;
  effectiveDate: Date;
  terminationDate?: Date;
  notes?: string;
}

interface CPTModifier {
  code: string;
  description: string;
  category: string;
  applicableWith: string[];
}

interface InsuranceCoverage {
  insuranceCompany: string;
  coverageLevel: 'covered' | 'limited' | 'not_covered' | 'prior_auth_required';
  copayAmount?: number;
  deductibleApplies: boolean;
  notes?: string;
}

interface CPTCodeLookupProps {
  onCodeSelect?: (code: CPTCode) => void;
  selectedCodes?: string[];
  maxSelections?: number;
  showModifiers?: boolean;
  showInsuranceCoverage?: boolean;
  placeholder?: string;
  disabled?: boolean;
}

const CPTCodeLookup: React.FC<CPTCodeLookupProps> = ({
  onCodeSelect,
  selectedCodes = [],
  maxSelections,
  showModifiers = true,
  showInsuranceCoverage = true,
  placeholder = "Search CPT/HCPCS codes...",
  disabled = false,
}) => {
  const [searchQuery, setSearchQuery] = useState('');
  const [searchResults, setSearchResults] = useState<CPTCode[]>([]);
  const [isSearching, setIsSearching] = useState(false);
  const [searchError, setSearchError] = useState<string | null>(null);
  const [favorites, setFavorites] = useState<CPTCode[]>([]);
  const [recentCodes, setRecentCodes] = useState<CPTCode[]>([]);
  const [selectedCode, setSelectedCode] = useState<CPTCode | null>(null);
  const [expandedCodes, setExpandedCodes] = useState<Set<string>>(new Set());
  const [categoryFilter, setCategoryFilter] = useState<string>('all');
  const [coverageDialog, setCoverageDialog] = useState<CPTCode | null>(null);

  const categories = [
    'Evaluation and Management',
    'Surgery',
    'Radiology',
    'Pathology and Laboratory',
    'Medicine',
    'Anesthesia',
    'HCPCS Level II',
  ];

  // Debounced search function
  const performSearch = useCallback(
    debounce(async (query: string) => {
      if (!query.trim()) {
        setSearchResults([]);
        return;
      }

      setIsSearching(true);
      setSearchError(null);

      try {
        const response = await fetch(`/api/medical-codes/cpt/search?q=${encodeURIComponent(query)}&category=${categoryFilter}`, {
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('token')}`,
            'Content-Type': 'application/json',
          },
        });

        if (!response.ok) {
          throw new Error('Failed to search CPT codes');
        }

        const data = await response.json();
        
        if (data.success) {
          setSearchResults(data.data || []);
        } else {
          throw new Error(data.error || 'Search failed');
        }
      } catch (error) {
        setSearchError(error instanceof Error ? error.message : 'Search failed');
        setSearchResults([]);
      } finally {
        setIsSearching(false);
      }
    }, 300),
    [categoryFilter]
  );

  useEffect(() => {
    performSearch(searchQuery);
  }, [searchQuery, performSearch]);

  useEffect(() => {
    loadFavorites();
    loadRecentCodes();
  }, []);

  const loadFavorites = async () => {
    try {
      const response = await fetch('/api/medical-codes/cpt/favorites', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
        },
      });
      
      if (response.ok) {
        const data = await response.json();
        setFavorites(data.data || []);
      }
    } catch (error) {
      console.error('Failed to load favorites:', error);
    }
  };

  const loadRecentCodes = async () => {
    try {
      const response = await fetch('/api/medical-codes/cpt/recent', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
        },
      });
      
      if (response.ok) {
        const data = await response.json();
        setRecentCodes(data.data || []);
      }
    } catch (error) {
      console.error('Failed to load recent codes:', error);
    }
  };

  const handleCodeSelect = (code: CPTCode) => {
    if (maxSelections && selectedCodes.length >= maxSelections) {
      return;
    }

    setSelectedCode(code);
    onCodeSelect?.(code);
    
    // Add to recent codes
    addToRecentCodes(code);
  };

  const addToRecentCodes = async (code: CPTCode) => {
    try {
      await fetch('/api/medical-codes/cpt/recent', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ code: code.code }),
      });
      
      // Update local recent codes
      setRecentCodes(prev => {
        const filtered = prev.filter(c => c.code !== code.code);
        return [code, ...filtered].slice(0, 10);
      });
    } catch (error) {
      console.error('Failed to add to recent codes:', error);
    }
  };

  const toggleFavorite = async (code: CPTCode) => {
    const isFavorite = favorites.some(fav => fav.code === code.code);
    
    try {
      const response = await fetch(`/api/medical-codes/cpt/favorites/${code.code}`, {
        method: isFavorite ? 'DELETE' : 'POST',
        headers: {
          'Authorization': `Bearer ${localStorage.getToken('token')}`,
          'Content-Type': 'application/json',
        },
      });
      
      if (response.ok) {
        if (isFavorite) {
          setFavorites(prev => prev.filter(fav => fav.code !== code.code));
        } else {
          setFavorites(prev => [...prev, code]);
        }
      }
    } catch (error) {
      console.error('Failed to toggle favorite:', error);
    }
  };

  const toggleCodeExpansion = (codeId: string) => {
    setExpandedCodes(prev => {
      const newSet = new Set(prev);
      if (newSet.has(codeId)) {
        newSet.delete(codeId);
      } else {
        newSet.add(codeId);
      }
      return newSet;
    });
  };

  const handleClearSearch = () => {
    setSearchQuery('');
    setSearchResults([]);
    setSelectedCode(null);
  };

  const formatCodeDisplay = (code: CPTCode) => {
    return {
      primary: `${code.code} - ${code.description}`,
      secondary: code.category,
      isSelected: selectedCodes.includes(code.code),
      isFavorite: favorites.some(fav => fav.code === code.code),
    };
  };

  const getCodeValidationIcon = (code: CPTCode) => {
    if (!code.isValid) {
      return <WarningIcon color="warning" />;
    }
    if (code.terminationDate && new Date(code.terminationDate) < new Date()) {
      return <WarningIcon color="error" />;
    }
    return <CheckCircleIcon color="success" />;
  };

  const getCoverageIcon = (code: CPTCode) => {
    if (!code.insuranceCoverage || code.insuranceCoverage.length === 0) {
      return null;
    }

    const coverageLevels = code.insuranceCoverage.map(c => c.coverageLevel);
    
    if (coverageLevels.includes('not_covered')) {
      return <WarningIcon color="error" />;
    }
    if (coverageLevels.includes('prior_auth_required')) {
      return <SecurityIcon color="warning" />;
    }
    if (coverageLevels.includes('covered')) {
      return <CheckCircleIcon color="success" />;
    }
    
    return <InfoIcon color="info" />;
  };

  const renderCodeItem = (code: CPTCode, showFavoriteButton: boolean = true) => {
    const display = formatCodeDisplay(code);
    const isExpanded = expandedCodes.has(code.code);
    
    return (
      <ListItem
        key={code.code}
        disablePadding
        secondaryAction={
          showFavoriteButton && (
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              {getCoverageIcon(code)}
              <IconButton
                edge="end"
                onClick={(e) => {
                  e.stopPropagation();
                  toggleFavorite(code);
                }}
              >
                {display.isFavorite ? <StarIcon color="primary" /> : <StarBorderIcon />}
              </IconButton>
            </Box>
          )
        }
      >
        <ListItemButton
          onClick={() => handleCodeSelect(code)}
          selected={display.isSelected}
          disabled={disabled || (maxSelections ? selectedCodes.length >= maxSelections && !display.isSelected : false)}
        >
          <Box sx={{ display: 'flex', alignItems: 'center', width: '100%' }}>
            <Box sx={{ mr: 1 }}>
              <Tooltip title={code.isValid ? 'Valid code' : 'Invalid code'}>
                {getCodeValidationIcon(code)}
              </Tooltip>
            </Box>
            
            <ListItemText
              primary={
                <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                  <Box>
                    <Typography variant="body1" component="span" sx={{ fontFamily: 'monospace', mr: 1 }}>
                      {code.code}
                    </Typography>
                    <Typography variant="body1" component="span">
                      {code.description}
                    </Typography>
                  </Box>
                  
                  <IconButton
                    size="small"
                    onClick={(e) => {
                      e.stopPropagation();
                      toggleCodeExpansion(code.code);
                    }}
                  >
                    {isExpanded ? <ExpandLessIcon /> : <ExpandMoreIcon />}
                  </IconButton>
                </Box>
              }
              secondary={
                <Box>
                  <Typography variant="body2" color="text.secondary">
                    {code.category}
                  </Typography>
                  {code.rvu && (
                    <Typography variant="caption" color="text.secondary">
                      RVU: {code.rvu.total.toFixed(2)} (Work: {code.rvu.work}, PE: {code.rvu.practice}, MP: {code.rvu.malpractice})
                    </Typography>
                  )}
                </Box>
              }
            />
            
            {display.isSelected && (
              <Chip label="Selected" size="small" color="primary" />
            )}
          </Box>
        </ListItemButton>
        
        <Collapse in={isExpanded}>
          <Box sx={{ p: 2, bgcolor: 'grey.50' }}>
            <Grid container spacing={2}>
              {code.modifiers && code.modifiers.length > 0 && (
                <Grid item xs={12}>
                  <Typography variant="subtitle2" sx={{ mb: 1 }}>
                    Available Modifiers
                  </Typography>
                  <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                    {code.modifiers.map((modifier) => (
                      <Tooltip key={modifier.code} title={modifier.description}>
                        <Chip
                          label={modifier.code}
                          size="small"
                          variant="outlined"
                          color="primary"
                        />
                      </Tooltip>
                    ))}
                  </Box>
                </Grid>
              )}
              
              {code.bundlingRules && code.bundlingRules.length > 0 && (
                <Grid item xs={12}>
                  <Typography variant="subtitle2" sx={{ mb: 1 }}>
                    Bundling Rules
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    {code.bundlingRules.join(', ')}
                  </Typography>
                </Grid>
              )}
              
              {showInsuranceCoverage && code.insuranceCoverage && (
                <Grid item xs={12}>
                  <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                    <Typography variant="subtitle2">
                      Insurance Coverage
                    </Typography>
                    <Button
                      size="small"
                      onClick={() => setCoverageDialog(code)}
                    >
                      View Details
                    </Button>
                  </Box>
                </Grid>
              )}
              
              {code.notes && (
                <Grid item xs={12}>
                  <Typography variant="subtitle2" sx={{ mb: 1 }}>
                    Notes
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    {code.notes}
                  </Typography>
                </Grid>
              )}
            </Grid>
          </Box>
        </Collapse>
      </ListItem>
    );
  };

  return (
    <>
      <Card sx={{ width: '100%', maxWidth: 800 }}>
        <CardContent>
          <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
            <MedicalIcon sx={{ mr: 1, color: 'primary.main' }} />
            <Typography variant="h6" component="h2">
              CPT/HCPCS Code Lookup
            </Typography>
            {maxSelections && (
              <Chip 
                label={`${selectedCodes.length}/${maxSelections} selected`} 
                size="small" 
                color={selectedCodes.length >= maxSelections ? 'warning' : 'default'}
                sx={{ ml: 'auto' }}
              />
            )}
          </Box>

          {/* Search Input */}
          <Box sx={{ display: 'flex', gap: 2, mb: 2 }}>
            <TextField
              fullWidth
              placeholder={placeholder}
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              disabled={disabled}
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <SearchIcon />
                  </InputAdornment>
                ),
                endAdornment: (
                  <InputAdornment position="end">
                    {(searchQuery || isSearching) && (
                      <IconButton
                        onClick={handleClearSearch}
                        disabled={disabled}
                        size="small"
                      >
                        {isSearching ? <CircularProgress size={20} /> : <ClearIcon />}
                      </IconButton>
                    )}
                  </InputAdornment>
                ),
              }}
            />
            
            <FormControl sx={{ minWidth: 200 }}>
              <InputLabel>Category</InputLabel>
              <Select
                value={categoryFilter}
                onChange={(e) => setCategoryFilter(e.target.value)}
                label="Category"
                disabled={disabled}
              >
                <MenuItem value="all">All Categories</MenuItem>
                {categories.map((category) => (
                  <MenuItem key={category} value={category}>
                    {category}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </Box>

          {/* Search Error */}
          {searchError && (
            <Alert severity="error" sx={{ mb: 2 }}>
              {searchError}
            </Alert>
          )}

          {/* Favorites Section */}
          {favorites.length > 0 && !searchQuery && (
            <Box sx={{ mb: 2 }}>
              <Typography variant="subtitle1" sx={{ mb: 1, display: 'flex', alignItems: 'center' }}>
                <StarIcon sx={{ mr: 1 }} />
                Favorite Codes
              </Typography>
              <List dense sx={{ maxHeight: 200, overflow: 'auto' }}>
                {favorites.slice(0, 5).map((code) => renderCodeItem(code, false))}
              </List>
              <Divider sx={{ my: 2 }} />
            </Box>
          )}

          {/* Recent Codes Section */}
          {recentCodes.length > 0 && !searchQuery && (
            <Box sx={{ mb: 2 }}>
              <Typography variant="subtitle1" sx={{ mb: 1, display: 'flex', alignItems: 'center' }}>
                <HistoryIcon sx={{ mr: 1 }} />
                Recently Used
              </Typography>
              <List dense sx={{ maxHeight: 200, overflow: 'auto' }}>
                {recentCodes.slice(0, 5).map((code) => renderCodeItem(code))}
              </List>
              <Divider sx={{ my: 2 }} />
            </Box>
          )}

          {/* Search Results */}
          {searchResults.length > 0 && (
            <Box>
              <Typography variant="subtitle1" sx={{ mb: 1 }}>
                Search Results ({searchResults.length})
              </Typography>
              <List sx={{ maxHeight: 400, overflow: 'auto' }}>
                {searchResults.map((code, index) => (
                  <React.Fragment key={code.code}>
                    {renderCodeItem(code)}
                    {index < searchResults.length - 1 && <Divider />}
                  </React.Fragment>
                ))}
              </List>
            </Box>
          )}

          {/* No Results */}
          {!isSearching && searchResults.length === 0 && searchQuery && (
            <Box sx={{ textAlign: 'center', py: 3 }}>
              <Typography variant="body2" color="text.secondary">
                No CPT/HCPCS codes found matching "{searchQuery}".
              </Typography>
              <Typography variant="caption" color="text.secondary">
                Try searching by code number, description, or category.
              </Typography>
            </Box>
          )}
        </CardContent>
      </Card>

      {/* Insurance Coverage Dialog */}
      <Dialog
        open={!!coverageDialog}
        onClose={() => setCoverageDialog(null)}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>Insurance Coverage Details</DialogTitle>
        <DialogContent>
          {coverageDialog && (
            <Box>
              <Typography variant="h6" sx={{ mb: 2 }}>
                {coverageDialog.code} - {coverageDialog.description}
              </Typography>
              
              {coverageDialog.insuranceCoverage?.map((coverage, index) => (
                <Card key={index} sx={{ mb: 2 }}>
                  <CardContent>
                    <Typography variant="subtitle1" sx={{ mb: 1 }}>
                      {coverage.insuranceCompany}
                    </Typography>
                    
                    <Grid container spacing={2}>
                      <Grid item xs={6}>
                        <Typography variant="body2" color="text.secondary">
                          Coverage Level
                        </Typography>
                        <Chip
                          label={coverage.coverageLevel.replace('_', ' ').toUpperCase()}
                          color={coverage.coverageLevel === 'covered' ? 'success' : 
                                 coverage.coverageLevel === 'not_covered' ? 'error' : 'warning'}
                          size="small"
                        />
                      </Grid>
                      
                      {coverage.copayAmount && (
                        <Grid item xs={6}>
                          <Typography variant="body2" color="text.secondary">
                            Copay Amount
                          </Typography>
                          <Typography variant="body1">
                            ${coverage.copayAmount}
                          </Typography>
                        </Grid>
                      )}
                      
                      <Grid item xs={6}>
                        <Typography variant="body2" color="text.secondary">
                          Deductible Applies
                        </Typography>
                        <Typography variant="body1">
                          {coverage.deductibleApplies ? 'Yes' : 'No'}
                        </Typography>
                      </Grid>
                    </Grid>
                    
                    {coverage.notes && (
                      <Typography variant="body2" sx={{ mt: 1 }}>
                        <strong>Notes:</strong> {coverage.notes}
                      </Typography>
                    )}
                  </CardContent>
                </Card>
              ))}
            </Box>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setCoverageDialog(null)}>Close</Button>
        </DialogActions>
      </Dialog>
    </>
  );
};

// Utility function for debouncing
function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout;
  return (...args: Parameters<T>) => {
    clearTimeout(timeout);
    timeout = setTimeout(() => func(...args), wait);
  };
}

export default CPTCodeLookup;
