import React, { useState, useEffect, useCallback } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  TextField,
  Autocomplete,
  Chip,
  List,
  ListItem,
  ListItemText,
  ListItemButton,
  Divider,
  Alert,
  CircularProgress,
  InputAdornment,
  IconButton,
  Tooltip,
  Badge,
  Collapse,
  TreeView,
  TreeItem,
} from '@mui/material';
import {
  Search as SearchIcon,
  Clear as ClearIcon,
  Star as StarIcon,
  StarBorder as StarBorderIcon,
  History as HistoryIcon,
  ExpandMore as ExpandMoreIcon,
  ChevronRight as ChevronRightIcon,
  Info as InfoIcon,
  CheckCircle as CheckCircleIcon,
  Warning as WarningIcon,
} from '@mui/icons-material';

interface ICD10Code {
  code: string;
  description: string;
  category: string;
  subcategory?: string;
  notes?: string;
  includes?: string[];
  excludes?: string[];
  isValid: boolean;
  billable: boolean;
  children?: ICD10Code[];
}

interface ICD10CodeSearchProps {
  onCodeSelect?: (code: ICD10Code) => void;
  selectedCodes?: string[];
  maxSelections?: number;
  showFavorites?: boolean;
  showRecent?: boolean;
  placeholder?: string;
  disabled?: boolean;
}

const ICD10CodeSearch: React.FC<ICD10CodeSearchProps> = ({
  onCodeSelect,
  selectedCodes = [],
  maxSelections,
  showFavorites = true,
  showRecent = true,
  placeholder = "Search ICD-10 codes...",
  disabled = false,
}) => {
  const [searchQuery, setSearchQuery] = useState('');
  const [searchResults, setSearchResults] = useState<ICD10Code[]>([]);
  const [isSearching, setIsSearching] = useState(false);
  const [searchError, setSearchError] = useState<string | null>(null);
  const [favorites, setFavorites] = useState<ICD10Code[]>([]);
  const [recentCodes, setRecentCodes] = useState<ICD10Code[]>([]);
  const [expandedCategories, setExpandedCategories] = useState<string[]>([]);
  const [selectedCode, setSelectedCode] = useState<ICD10Code | null>(null);

  // Debounced search function
  const performSearch = useCallback(
    debounce(async (query: string) => {
      if (!query.trim()) {
        setSearchResults([]);
        return;
      }

      setIsSearching(true);
      setSearchError(null);

      try {
        const response = await fetch(`/api/medical-codes/icd10/search?q=${encodeURIComponent(query)}`, {
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('token')}`,
            'Content-Type': 'application/json',
          },
        });

        if (!response.ok) {
          throw new Error('Failed to search ICD-10 codes');
        }

        const data = await response.json();
        
        if (data.success) {
          setSearchResults(data.data || []);
        } else {
          throw new Error(data.error || 'Search failed');
        }
      } catch (error) {
        setSearchError(error instanceof Error ? error.message : 'Search failed');
        setSearchResults([]);
      } finally {
        setIsSearching(false);
      }
    }, 300),
    []
  );

  useEffect(() => {
    performSearch(searchQuery);
  }, [searchQuery, performSearch]);

  useEffect(() => {
    loadFavorites();
    loadRecentCodes();
  }, []);

  const loadFavorites = async () => {
    try {
      const response = await fetch('/api/medical-codes/icd10/favorites', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
        },
      });
      
      if (response.ok) {
        const data = await response.json();
        setFavorites(data.data || []);
      }
    } catch (error) {
      console.error('Failed to load favorites:', error);
    }
  };

  const loadRecentCodes = async () => {
    try {
      const response = await fetch('/api/medical-codes/icd10/recent', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
        },
      });
      
      if (response.ok) {
        const data = await response.json();
        setRecentCodes(data.data || []);
      }
    } catch (error) {
      console.error('Failed to load recent codes:', error);
    }
  };

  const handleCodeSelect = (code: ICD10Code) => {
    if (maxSelections && selectedCodes.length >= maxSelections) {
      return;
    }

    setSelectedCode(code);
    onCodeSelect?.(code);
    
    // Add to recent codes
    addToRecentCodes(code);
  };

  const addToRecentCodes = async (code: ICD10Code) => {
    try {
      await fetch('/api/medical-codes/icd10/recent', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ code: code.code }),
      });
      
      // Update local recent codes
      setRecentCodes(prev => {
        const filtered = prev.filter(c => c.code !== code.code);
        return [code, ...filtered].slice(0, 10); // Keep only 10 recent codes
      });
    } catch (error) {
      console.error('Failed to add to recent codes:', error);
    }
  };

  const toggleFavorite = async (code: ICD10Code) => {
    const isFavorite = favorites.some(fav => fav.code === code.code);
    
    try {
      const response = await fetch(`/api/medical-codes/icd10/favorites/${code.code}`, {
        method: isFavorite ? 'DELETE' : 'POST',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
          'Content-Type': 'application/json',
        },
      });
      
      if (response.ok) {
        if (isFavorite) {
          setFavorites(prev => prev.filter(fav => fav.code !== code.code));
        } else {
          setFavorites(prev => [...prev, code]);
        }
      }
    } catch (error) {
      console.error('Failed to toggle favorite:', error);
    }
  };

  const handleClearSearch = () => {
    setSearchQuery('');
    setSearchResults([]);
    setSelectedCode(null);
  };

  const formatCodeDisplay = (code: ICD10Code) => {
    return {
      primary: `${code.code} - ${code.description}`,
      secondary: code.category,
      isSelected: selectedCodes.includes(code.code),
      isFavorite: favorites.some(fav => fav.code === code.code),
    };
  };

  const getCodeValidationIcon = (code: ICD10Code) => {
    if (!code.isValid) {
      return <WarningIcon color="warning" />;
    }
    if (code.billable) {
      return <CheckCircleIcon color="success" />;
    }
    return <InfoIcon color="info" />;
  };

  const renderCodeItem = (code: ICD10Code, showFavoriteButton: boolean = true) => {
    const display = formatCodeDisplay(code);
    
    return (
      <ListItem
        key={code.code}
        disablePadding
        secondaryAction={
          showFavoriteButton && (
            <IconButton
              edge="end"
              onClick={(e) => {
                e.stopPropagation();
                toggleFavorite(code);
              }}
            >
              {display.isFavorite ? <StarIcon color="primary" /> : <StarBorderIcon />}
            </IconButton>
          )
        }
      >
        <ListItemButton
          onClick={() => handleCodeSelect(code)}
          selected={display.isSelected}
          disabled={disabled || (maxSelections ? selectedCodes.length >= maxSelections && !display.isSelected : false)}
        >
          <Box sx={{ display: 'flex', alignItems: 'center', width: '100%' }}>
            <Box sx={{ mr: 1 }}>
              <Tooltip title={code.billable ? 'Billable code' : code.isValid ? 'Valid code' : 'Invalid code'}>
                {getCodeValidationIcon(code)}
              </Tooltip>
            </Box>
            
            <ListItemText
              primary={
                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                  <Typography variant="body1" component="span" sx={{ fontFamily: 'monospace', mr: 1 }}>
                    {code.code}
                  </Typography>
                  <Typography variant="body1" component="span">
                    {code.description}
                  </Typography>
                </Box>
              }
              secondary={
                <Box>
                  <Typography variant="body2" color="text.secondary">
                    {code.category}
                  </Typography>
                  {code.notes && (
                    <Typography variant="caption" color="text.secondary">
                      {code.notes}
                    </Typography>
                  )}
                </Box>
              }
            />
            
            {display.isSelected && (
              <Chip label="Selected" size="small" color="primary" />
            )}
          </Box>
        </ListItemButton>
      </ListItem>
    );
  };

  return (
    <Card sx={{ width: '100%', maxWidth: 800 }}>
      <CardContent>
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
          <Typography variant="h6" component="h2">
            ICD-10 Code Search
          </Typography>
          {maxSelections && (
            <Chip 
              label={`${selectedCodes.length}/${maxSelections} selected`} 
              size="small" 
              color={selectedCodes.length >= maxSelections ? 'warning' : 'default'}
              sx={{ ml: 'auto' }}
            />
          )}
        </Box>

        {/* Search Input */}
        <TextField
          fullWidth
          placeholder={placeholder}
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
          disabled={disabled}
          InputProps={{
            startAdornment: (
              <InputAdornment position="start">
                <SearchIcon />
              </InputAdornment>
            ),
            endAdornment: (
              <InputAdornment position="end">
                {(searchQuery || isSearching) && (
                  <IconButton
                    onClick={handleClearSearch}
                    disabled={disabled}
                    size="small"
                  >
                    {isSearching ? <CircularProgress size={20} /> : <ClearIcon />}
                  </IconButton>
                )}
              </InputAdornment>
            ),
          }}
          sx={{ mb: 2 }}
        />

        {/* Search Error */}
        {searchError && (
          <Alert severity="error" sx={{ mb: 2 }}>
            {searchError}
          </Alert>
        )}

        {/* Favorites Section */}
        {showFavorites && favorites.length > 0 && !searchQuery && (
          <Box sx={{ mb: 2 }}>
            <Typography variant="subtitle1" sx={{ mb: 1, display: 'flex', alignItems: 'center' }}>
              <StarIcon sx={{ mr: 1 }} />
              Favorite Codes
            </Typography>
            <List dense sx={{ maxHeight: 200, overflow: 'auto' }}>
              {favorites.slice(0, 5).map((code) => renderCodeItem(code, false))}
            </List>
            <Divider sx={{ my: 2 }} />
          </Box>
        )}

        {/* Recent Codes Section */}
        {showRecent && recentCodes.length > 0 && !searchQuery && (
          <Box sx={{ mb: 2 }}>
            <Typography variant="subtitle1" sx={{ mb: 1, display: 'flex', alignItems: 'center' }}>
              <HistoryIcon sx={{ mr: 1 }} />
              Recently Used
            </Typography>
            <List dense sx={{ maxHeight: 200, overflow: 'auto' }}>
              {recentCodes.slice(0, 5).map((code) => renderCodeItem(code))}
            </List>
            <Divider sx={{ my: 2 }} />
          </Box>
        )}

        {/* Search Results */}
        {searchResults.length > 0 && (
          <Box>
            <Typography variant="subtitle1" sx={{ mb: 1 }}>
              Search Results ({searchResults.length})
            </Typography>
            <List sx={{ maxHeight: 400, overflow: 'auto' }}>
              {searchResults.map((code, index) => (
                <React.Fragment key={code.code}>
                  {renderCodeItem(code)}
                  {index < searchResults.length - 1 && <Divider />}
                </React.Fragment>
              ))}
            </List>
          </Box>
        )}

        {/* No Results */}
        {!isSearching && searchResults.length === 0 && searchQuery && (
          <Box sx={{ textAlign: 'center', py: 3 }}>
            <Typography variant="body2" color="text.secondary">
              No ICD-10 codes found matching "{searchQuery}".
            </Typography>
            <Typography variant="caption" color="text.secondary">
              Try searching by code number, description, or category.
            </Typography>
          </Box>
        )}

        {/* Selected Code Details */}
        {selectedCode && (
          <Box sx={{ mt: 2, p: 2, bgcolor: 'grey.50', borderRadius: 1 }}>
            <Typography variant="subtitle2" sx={{ mb: 1 }}>
              Selected Code Details
            </Typography>
            <Typography variant="body1" sx={{ fontFamily: 'monospace', mb: 1 }}>
              {selectedCode.code} - {selectedCode.description}
            </Typography>
            <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
              Category: {selectedCode.category}
            </Typography>
            
            {selectedCode.notes && (
              <Typography variant="body2" sx={{ mb: 1 }}>
                <strong>Notes:</strong> {selectedCode.notes}
              </Typography>
            )}
            
            {selectedCode.includes && selectedCode.includes.length > 0 && (
              <Typography variant="body2" sx={{ mb: 1 }}>
                <strong>Includes:</strong> {selectedCode.includes.join(', ')}
              </Typography>
            )}
            
            {selectedCode.excludes && selectedCode.excludes.length > 0 && (
              <Typography variant="body2" color="warning.main">
                <strong>Excludes:</strong> {selectedCode.excludes.join(', ')}
              </Typography>
            )}
            
            <Box sx={{ display: 'flex', gap: 1, mt: 1 }}>
              <Chip 
                label={selectedCode.billable ? 'Billable' : 'Non-billable'} 
                size="small" 
                color={selectedCode.billable ? 'success' : 'default'} 
              />
              <Chip 
                label={selectedCode.isValid ? 'Valid' : 'Invalid'} 
                size="small" 
                color={selectedCode.isValid ? 'success' : 'error'} 
              />
            </Box>
          </Box>
        )}
      </CardContent>
    </Card>
  );
};

// Utility function for debouncing
function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout;
  return (...args: Parameters<T>) => {
    clearTimeout(timeout);
    timeout = setTimeout(() => func(...args), wait);
  };
}

export default ICD10CodeSearch;
