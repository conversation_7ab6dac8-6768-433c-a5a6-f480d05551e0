// OCTAVE Healthcare Platform - PhilHealth Integration Component
// UI for PhilHealth API connection and management

import React, { useState, useEffect } from 'react';
import {
  Box,
  Card,
  CardContent,
  CardHeader,
  TextField,
  Button,
  Typography,
  Alert,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Chip,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  CircularProgress,
  useTheme,
} from '@mui/material';
import {
  CloudRounded,
  CloudOffRounded,
  SettingsRounded,
  CheckCircleRounded,
  ErrorRounded,
  RefreshRounded,
  VisibilityRounded,
  VisibilityOffRounded,
} from '@mui/icons-material';

import { philHealthService, PhilHealthCredentials, PhilHealthConnectionStatus } from '@/services/philHealthService';

interface PhilHealthIntegrationProps {
  onConnectionChange?: (status: PhilHealthConnectionStatus) => void;
}

export const PhilHealthIntegration: React.FC<PhilHealthIntegrationProps> = ({
  onConnectionChange,
}) => {
  const theme = useTheme();

  // State
  const [connectionStatus, setConnectionStatus] = useState<PhilHealthConnectionStatus>(
    philHealthService.getConnectionStatus()
  );
  const [showSetupDialog, setShowSetupDialog] = useState(false);
  const [isConnecting, setIsConnecting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [showPassword, setShowPassword] = useState(false);

  // Form state
  const [credentials, setCredentials] = useState<PhilHealthCredentials>({
    facilityCode: '',
    username: '',
    password: '',
    environment: 'sandbox',
  });

  useEffect(() => {
    // Check connection status on mount
    updateConnectionStatus();
  }, []);

  const updateConnectionStatus = () => {
    const status = philHealthService.getConnectionStatus();
    setConnectionStatus(status);
    onConnectionChange?.(status);
  };

  const handleConnect = async () => {
    if (!credentials.facilityCode || !credentials.username || !credentials.password) {
      setError('Please fill in all required fields');
      return;
    }

    setIsConnecting(true);
    setError(null);

    try {
      await philHealthService.authenticate(credentials);
      updateConnectionStatus();
      setShowSetupDialog(false);
      
      // Clear password from form for security
      setCredentials(prev => ({ ...prev, password: '' }));
    } catch (error: any) {
      setError(error.message);
    } finally {
      setIsConnecting(false);
    }
  };

  const handleDisconnect = async () => {
    try {
      await philHealthService.disconnect();
      updateConnectionStatus();
    } catch (error: any) {
      setError(error.message);
    }
  };

  const handleInputChange = (field: keyof PhilHealthCredentials) => (
    event: React.ChangeEvent<HTMLInputElement | { value: unknown }>
  ) => {
    setCredentials(prev => ({ ...prev, [field]: event.target.value }));
    if (error) setError(null);
  };

  const getStatusColor = () => {
    if (connectionStatus.isConnected) return theme.palette.success.main;
    if (connectionStatus.connectionError) return theme.palette.error.main;
    return theme.palette.grey[500];
  };

  const getStatusIcon = () => {
    if (connectionStatus.isConnected) return <CheckCircleRounded />;
    if (connectionStatus.connectionError) return <ErrorRounded />;
    return <CloudOffRounded />;
  };

  return (
    <Card>
      <CardHeader
        title="PhilHealth Integration"
        subheader="Connect to PhilHealth API for eligibility checks and claims"
        action={
          <IconButton onClick={() => setShowSetupDialog(true)}>
            <SettingsRounded />
          </IconButton>
        }
      />
      <CardContent>
        {/* Connection Status */}
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 3 }}>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            {getStatusIcon()}
            <Typography variant="body1" fontWeight="medium">
              {connectionStatus.isConnected ? 'Connected' : 'Disconnected'}
            </Typography>
          </Box>

          <Chip
            label={connectionStatus.environment.toUpperCase()}
            size="small"
            color={connectionStatus.environment === 'production' ? 'error' : 'warning'}
          />

          {connectionStatus.facilityCode && (
            <Chip
              label={`Facility: ${connectionStatus.facilityCode}`}
              size="small"
              variant="outlined"
            />
          )}
        </Box>

        {/* Connection Details */}
        {connectionStatus.isConnected && connectionStatus.lastConnected && (
          <Typography variant="body2" color="text.secondary" gutterBottom>
            Last connected: {connectionStatus.lastConnected.toLocaleString()}
          </Typography>
        )}

        {connectionStatus.connectionError && (
          <Alert severity="error" sx={{ mb: 2 }}>
            {connectionStatus.connectionError}
          </Alert>
        )}

        {/* Action Buttons */}
        <Box sx={{ display: 'flex', gap: 2 }}>
          {connectionStatus.isConnected ? (
            <>
              <Button
                variant="outlined"
                startIcon={<RefreshRounded />}
                onClick={updateConnectionStatus}
              >
                Refresh Status
              </Button>
              <Button
                variant="outlined"
                color="error"
                startIcon={<CloudOffRounded />}
                onClick={handleDisconnect}
              >
                Disconnect
              </Button>
            </>
          ) : (
            <Button
              variant="contained"
              startIcon={<CloudRounded />}
              onClick={() => setShowSetupDialog(true)}
            >
              Connect to PhilHealth
            </Button>
          )}
        </Box>

        {/* Setup Instructions */}
        {!connectionStatus.isConnected && (
          <Alert severity="info" sx={{ mt: 2 }}>
            <Typography variant="body2">
              <strong>Setup Required:</strong> To connect to PhilHealth, you'll need:
              <br />
              • Facility Code from PhilHealth
              <br />
              • API Username and Password
              <br />
              • Choose between Sandbox (testing) or Production environment
            </Typography>
          </Alert>
        )}
      </CardContent>

      {/* Setup Dialog */}
      <Dialog
        open={showSetupDialog}
        onClose={() => setShowSetupDialog(false)}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <CloudRounded />
            <Typography variant="h6">PhilHealth Connection Setup</Typography>
          </Box>
        </DialogTitle>
        <DialogContent>
          {error && (
            <Alert severity="error" sx={{ mb: 2 }}>
              {error}
            </Alert>
          )}

          <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2, mt: 1 }}>
            <FormControl fullWidth>
              <InputLabel>Environment</InputLabel>
              <Select
                value={credentials.environment}
                onChange={handleInputChange('environment')}
                label="Environment"
              >
                <MenuItem value="sandbox">Sandbox (Testing)</MenuItem>
                <MenuItem value="production">Production</MenuItem>
              </Select>
            </FormControl>

            <TextField
              fullWidth
              label="Facility Code"
              value={credentials.facilityCode}
              onChange={handleInputChange('facilityCode')}
              placeholder="Enter your PhilHealth facility code"
              helperText="Your registered facility code with PhilHealth"
            />

            <TextField
              fullWidth
              label="API Username"
              value={credentials.username}
              onChange={handleInputChange('username')}
              placeholder="Enter your API username"
              autoComplete="username"
            />

            <TextField
              fullWidth
              label="API Password"
              type={showPassword ? 'text' : 'password'}
              value={credentials.password}
              onChange={handleInputChange('password')}
              placeholder="Enter your API password"
              autoComplete="current-password"
              InputProps={{
                endAdornment: (
                  <IconButton
                    onClick={() => setShowPassword(!showPassword)}
                    edge="end"
                  >
                    {showPassword ? <VisibilityOffRounded /> : <VisibilityRounded />}
                  </IconButton>
                ),
              }}
            />
          </Box>

          <Alert severity="warning" sx={{ mt: 2 }}>
            <Typography variant="body2">
              <strong>Security Notice:</strong> Your credentials are stored securely and used only 
              for PhilHealth API authentication. Passwords are not permanently stored.
            </Typography>
          </Alert>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setShowSetupDialog(false)} disabled={isConnecting}>
            Cancel
          </Button>
          <Button
            onClick={handleConnect}
            variant="contained"
            disabled={isConnecting || !credentials.facilityCode || !credentials.username || !credentials.password}
            startIcon={isConnecting ? <CircularProgress size={20} /> : <CloudRounded />}
          >
            {isConnecting ? 'Connecting...' : 'Connect'}
          </Button>
        </DialogActions>
      </Dialog>
    </Card>
  );
};

export default PhilHealthIntegration;
