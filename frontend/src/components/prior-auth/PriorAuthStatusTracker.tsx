import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  Card,
  CardContent,
  Typo<PERSON>,
  Stepper,
  Step,
  StepLabel,
  StepContent,
  Chip,
  LinearProgress,
  Alert,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Divider,
  IconButton,
  Tooltip,
  Grid,
} from '@mui/material';
import {
  CheckCircle as CheckCircleIcon,
  Schedule as ScheduleIcon,
  Error as ErrorIcon,
  Warning as WarningIcon,
  Info as InfoIcon,
  Refresh as RefreshIcon,
  Timeline as TimelineIcon,
  Notifications as NotificationsIcon,
  Assignment as AssignmentIcon,
} from '@mui/icons-material';
import { PriorAuthorization, PriorAuthStatus, Communication } from '../../types';
import { formatDate, formatRelativeTime, formatStatus } from '../../utils/formatters';

interface PriorAuthStatusTrackerProps {
  priorAuth: PriorAuthorization;
  onRefresh?: () => void;
  showDetailedTimeline?: boolean;
  compact?: boolean;
}

interface StatusStep {
  label: string;
  status: 'completed' | 'active' | 'pending' | 'error';
  date?: Date;
  description?: string;
  icon?: React.ReactNode;
}

const PriorAuthStatusTracker: React.FC<PriorAuthStatusTrackerProps> = ({
  priorAuth,
  onRefresh,
  showDetailedTimeline = false,
  compact = false,
}) => {
  const [timelineDialog, setTimelineDialog] = useState(false);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [estimatedCompletion, setEstimatedCompletion] = useState<Date | null>(null);

  useEffect(() => {
    calculateEstimatedCompletion();
  }, [priorAuth]);

  const calculateEstimatedCompletion = () => {
    // Calculate estimated completion based on status and historical data
    const submittedDate = priorAuth.submittedDate;
    if (!submittedDate) return;

    const businessDays = getBusinessDaysToAdd(priorAuth.status);
    const estimated = addBusinessDays(new Date(submittedDate), businessDays);
    setEstimatedCompletion(estimated);
  };

  const getBusinessDaysToAdd = (status: PriorAuthStatus): number => {
    // Typical processing times by status
    switch (status) {
      case PriorAuthStatus.SUBMITTED:
        return 3; // 3 business days to start review
      case PriorAuthStatus.UNDER_REVIEW:
        return 7; // 7 business days for review
      case PriorAuthStatus.MORE_INFO_REQUIRED:
        return 14; // 14 business days if more info needed
      default:
        return 0;
    }
  };

  const addBusinessDays = (date: Date, days: number): Date => {
    const result = new Date(date);
    let addedDays = 0;
    
    while (addedDays < days) {
      result.setDate(result.getDate() + 1);
      // Skip weekends (0 = Sunday, 6 = Saturday)
      if (result.getDay() !== 0 && result.getDay() !== 6) {
        addedDays++;
      }
    }
    
    return result;
  };

  const getStatusSteps = (): StatusStep[] => {
    const steps: StatusStep[] = [
      {
        label: 'Request Created',
        status: 'completed',
        date: priorAuth.createdAt,
        description: 'Prior authorization request created',
        icon: <AssignmentIcon />,
      },
      {
        label: 'Submitted to Insurance',
        status: priorAuth.submittedDate ? 'completed' : 'pending',
        date: priorAuth.submittedDate,
        description: 'Request submitted to insurance company',
        icon: <CheckCircleIcon />,
      },
      {
        label: 'Under Review',
        status: getStepStatus(PriorAuthStatus.UNDER_REVIEW),
        description: 'Insurance company reviewing request',
        icon: <ScheduleIcon />,
      },
      {
        label: 'Decision Made',
        status: getStepStatus([PriorAuthStatus.APPROVED, PriorAuthStatus.DENIED]),
        date: priorAuth.approvedDate || priorAuth.deniedDate,
        description: priorAuth.approvedDate ? 'Request approved' : priorAuth.deniedDate ? 'Request denied' : 'Awaiting decision',
        icon: priorAuth.approvedDate ? <CheckCircleIcon /> : priorAuth.deniedDate ? <ErrorIcon /> : <ScheduleIcon />,
      },
    ];

    return steps;
  };

  const getStepStatus = (targetStatus: PriorAuthStatus | PriorAuthStatus[]): 'completed' | 'active' | 'pending' | 'error' => {
    const statuses = Array.isArray(targetStatus) ? targetStatus : [targetStatus];
    
    if (statuses.includes(priorAuth.status)) {
      return priorAuth.status === PriorAuthStatus.DENIED ? 'error' : 'active';
    }
    
    // Check if this step has been completed
    const statusOrder = [
      PriorAuthStatus.DRAFT,
      PriorAuthStatus.SUBMITTED,
      PriorAuthStatus.UNDER_REVIEW,
      PriorAuthStatus.MORE_INFO_REQUIRED,
      PriorAuthStatus.APPROVED,
      PriorAuthStatus.DENIED,
    ];
    
    const currentIndex = statusOrder.indexOf(priorAuth.status);
    const targetIndex = Math.min(...statuses.map(s => statusOrder.indexOf(s)));
    
    if (currentIndex > targetIndex) {
      return 'completed';
    }
    
    return 'pending';
  };

  const getProgressPercentage = (): number => {
    const statusProgress = {
      [PriorAuthStatus.DRAFT]: 10,
      [PriorAuthStatus.SUBMITTED]: 25,
      [PriorAuthStatus.UNDER_REVIEW]: 50,
      [PriorAuthStatus.MORE_INFO_REQUIRED]: 40,
      [PriorAuthStatus.APPROVED]: 100,
      [PriorAuthStatus.DENIED]: 100,
      [PriorAuthStatus.EXPIRED]: 100,
      [PriorAuthStatus.CANCELLED]: 100,
      [PriorAuthStatus.COMPLETED]: 100,
      [PriorAuthStatus.APPEAL]: 60,
    };
    
    return statusProgress[priorAuth.status] || 0;
  };

  const handleRefresh = async () => {
    if (!onRefresh) return;
    
    setIsRefreshing(true);
    try {
      await onRefresh();
    } finally {
      setIsRefreshing(false);
    }
  };

  const getStatusColor = (status: PriorAuthStatus) => {
    const statusColors = {
      [PriorAuthStatus.DRAFT]: 'info',
      [PriorAuthStatus.SUBMITTED]: 'warning',
      [PriorAuthStatus.UNDER_REVIEW]: 'warning',
      [PriorAuthStatus.MORE_INFO_REQUIRED]: 'warning',
      [PriorAuthStatus.APPROVED]: 'success',
      [PriorAuthStatus.DENIED]: 'error',
      [PriorAuthStatus.EXPIRED]: 'error',
      [PriorAuthStatus.CANCELLED]: 'default',
      [PriorAuthStatus.COMPLETED]: 'success',
      [PriorAuthStatus.APPEAL]: 'info',
    } as const;
    
    return statusColors[status] || 'default';
  };

  const steps = getStatusSteps();
  const progressPercentage = getProgressPercentage();
  const statusInfo = formatStatus(priorAuth.status);

  if (compact) {
    return (
      <Card sx={{ mb: 2 }}>
        <CardContent sx={{ py: 2 }}>
          <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
            <Box>
              <Typography variant="h6">
                {priorAuth.trackingId}
              </Typography>
              <Chip 
                label={statusInfo.text} 
                color={getStatusColor(priorAuth.status)} 
                size="small" 
              />
            </Box>
            
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              <Typography variant="body2" color="text.secondary">
                {progressPercentage}% Complete
              </Typography>
              <Tooltip title="View detailed timeline">
                <IconButton onClick={() => setTimelineDialog(true)} size="small">
                  <TimelineIcon />
                </IconButton>
              </Tooltip>
            </Box>
          </Box>
          
          <LinearProgress 
            variant="determinate" 
            value={progressPercentage} 
            sx={{ mt: 1 }}
            color={getStatusColor(priorAuth.status)}
          />
        </CardContent>
      </Card>
    );
  }

  return (
    <>
      <Card>
        <CardContent>
          {/* Header */}
          <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 3 }}>
            <Box>
              <Typography variant="h5" component="h1">
                Prior Authorization Status
              </Typography>
              <Typography variant="h6" color="primary">
                {priorAuth.trackingId}
              </Typography>
            </Box>
            
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              <Chip 
                label={statusInfo.text} 
                color={getStatusColor(priorAuth.status)} 
                size="large"
              />
              <Tooltip title="Refresh status">
                <IconButton onClick={handleRefresh} disabled={isRefreshing}>
                  <RefreshIcon />
                </IconButton>
              </Tooltip>
            </Box>
          </Box>

          {/* Progress Bar */}
          <Box sx={{ mb: 3 }}>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
              <Typography variant="body2" color="text.secondary">
                Progress
              </Typography>
              <Typography variant="body2" color="text.secondary">
                {progressPercentage}% Complete
              </Typography>
            </Box>
            <LinearProgress 
              variant="determinate" 
              value={progressPercentage} 
              sx={{ height: 8, borderRadius: 4 }}
              color={getStatusColor(priorAuth.status)}
            />
          </Box>

          {/* Status Information */}
          <Grid container spacing={3} sx={{ mb: 3 }}>
            <Grid item xs={12} sm={6}>
              <Typography variant="subtitle2" color="text.secondary">
                Current Status
              </Typography>
              <Typography variant="body1">
                {statusInfo.text}
              </Typography>
            </Grid>
            
            <Grid item xs={12} sm={6}>
              <Typography variant="subtitle2" color="text.secondary">
                Last Updated
              </Typography>
              <Typography variant="body1">
                {formatRelativeTime(priorAuth.updatedAt)}
              </Typography>
            </Grid>
            
            {estimatedCompletion && (
              <Grid item xs={12} sm={6}>
                <Typography variant="subtitle2" color="text.secondary">
                  Estimated Completion
                </Typography>
                <Typography variant="body1">
                  {formatDate(estimatedCompletion)}
                </Typography>
              </Grid>
            )}
            
            {priorAuth.authorizationNumber && (
              <Grid item xs={12} sm={6}>
                <Typography variant="subtitle2" color="text.secondary">
                  Authorization Number
                </Typography>
                <Typography variant="body1" sx={{ fontFamily: 'monospace' }}>
                  {priorAuth.authorizationNumber}
                </Typography>
              </Grid>
            )}
          </Grid>

          {/* Denial Reason */}
          {priorAuth.status === PriorAuthStatus.DENIED && priorAuth.denialReason && (
            <Alert severity="error" sx={{ mb: 3 }}>
              <Typography variant="subtitle2">Denial Reason:</Typography>
              <Typography variant="body2">{priorAuth.denialReason}</Typography>
            </Alert>
          )}

          {/* Expiration Warning */}
          {priorAuth.expirationDate && new Date(priorAuth.expirationDate) < new Date() && (
            <Alert severity="warning" sx={{ mb: 3 }}>
              This authorization expired on {formatDate(priorAuth.expirationDate)}
            </Alert>
          )}

          {/* Status Timeline */}
          <Box>
            <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 2 }}>
              <Typography variant="h6">Status Timeline</Typography>
              <Button
                variant="outlined"
                size="small"
                onClick={() => setTimelineDialog(true)}
                startIcon={<TimelineIcon />}
              >
                View Detailed Timeline
              </Button>
            </Box>
            
            <Stepper orientation="vertical">
              {steps.map((step, index) => (
                <Step key={index} active={step.status === 'active'} completed={step.status === 'completed'}>
                  <StepLabel
                    error={step.status === 'error'}
                    icon={step.icon}
                  >
                    <Box>
                      <Typography variant="subtitle2">
                        {step.label}
                      </Typography>
                      {step.date && (
                        <Typography variant="caption" color="text.secondary">
                          {formatDate(step.date)} • {formatRelativeTime(step.date)}
                        </Typography>
                      )}
                    </Box>
                  </StepLabel>
                  <StepContent>
                    <Typography variant="body2" color="text.secondary">
                      {step.description}
                    </Typography>
                  </StepContent>
                </Step>
              ))}
            </Stepper>
          </Box>
        </CardContent>
      </Card>

      {/* Detailed Timeline Dialog */}
      <Dialog
        open={timelineDialog}
        onClose={() => setTimelineDialog(false)}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>
          Detailed Timeline - {priorAuth.trackingId}
        </DialogTitle>
        <DialogContent>
          <List>
            {priorAuth.communications.map((comm, index) => (
              <React.Fragment key={comm.id}>
                <ListItem>
                  <ListItemIcon>
                    <NotificationsIcon />
                  </ListItemIcon>
                  <ListItemText
                    primary={comm.subject}
                    secondary={
                      <Box>
                        <Typography variant="body2" color="text.secondary">
                          {comm.type} • {formatDateTime(comm.createdAt)}
                        </Typography>
                        <Typography variant="body2">
                          {comm.content}
                        </Typography>
                      </Box>
                    }
                  />
                </ListItem>
                {index < priorAuth.communications.length - 1 && <Divider />}
              </React.Fragment>
            ))}
          </List>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setTimelineDialog(false)}>Close</Button>
        </DialogActions>
      </Dialog>
    </>
  );
};

export default PriorAuthStatusTracker;
