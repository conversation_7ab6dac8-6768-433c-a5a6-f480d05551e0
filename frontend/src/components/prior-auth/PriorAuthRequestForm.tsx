import React, { useState, useEffect } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  TextField,
  Button,
  Grid,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Autocomplete,
  Chip,
  Alert,
  Stepper,
  Step,
  StepLabel,
  StepContent,
  IconButton,
  InputAdornment,
  Divider,
  List,
  ListItem,
  ListItemText,
  ListItemSecondaryAction,
} from '@mui/material';
import {
  Add as AddIcon,
  Remove as RemoveIcon,
  Search as SearchIcon,
  AttachFile as AttachFileIcon,
  Save as SaveIcon,
  Send as SendIcon,
  Cancel as CancelIcon,
  Security as SecurityIcon,
  LocalHospital as MedicalIcon,
  Assignment as AssignmentIcon,
} from '@mui/icons-material';
import { useFormik } from 'formik';
import * as Yup from 'yup';
import { PriorAuthorization, Priority, Patient, DocumentType } from '../../types';
import { useAppDispatch, useAppSelector } from '../../store';
import PatientSearch from '../patients/PatientSearch';

interface PriorAuthRequestFormProps {
  onSuccess?: (priorAuth: PriorAuthorization) => void;
  onCancel?: () => void;
  initialData?: Partial<PriorAuthorization>;
  selectedPatient?: Patient;
}

const steps = [
  'Patient Selection',
  'Procedure Information',
  'Clinical Justification',
  'Supporting Documents',
  'Review & Submit'
];

const validationSchema = Yup.object({
  patientId: Yup.string().required('Patient selection is required'),
  procedureCode: Yup.string().required('Procedure code is required'),
  procedureDescription: Yup.string().required('Procedure description is required'),
  diagnosisCodes: Yup.array()
    .of(Yup.string())
    .min(1, 'At least one diagnosis code is required'),
  clinicalJustification: Yup.string()
    .required('Clinical justification is required')
    .min(50, 'Clinical justification must be at least 50 characters'),
  priority: Yup.string().required('Priority is required'),
  insuranceId: Yup.string().required('Insurance selection is required'),
});

const PriorAuthRequestForm: React.FC<PriorAuthRequestFormProps> = ({
  onSuccess,
  onCancel,
  initialData,
  selectedPatient,
}) => {
  const dispatch = useAppDispatch();
  const { user } = useAppSelector(state => state.auth);
  
  const [activeStep, setActiveStep] = useState(0);
  const [selectedPatientData, setSelectedPatientData] = useState<Patient | null>(selectedPatient || null);
  const [attachedDocuments, setAttachedDocuments] = useState<File[]>([]);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isDraft, setIsDraft] = useState(false);

  const formik = useFormik({
    initialValues: {
      patientId: selectedPatient?.id || initialData?.patientId || '',
      procedureCode: initialData?.procedureCode || '',
      procedureDescription: initialData?.procedureDescription || '',
      diagnosisCodes: initialData?.diagnosisCodes || [''],
      clinicalJustification: initialData?.clinicalJustification || '',
      priority: initialData?.priority || Priority.ROUTINE,
      insuranceId: initialData?.insuranceId || '',
      requestedDate: new Date().toISOString().split('T')[0],
      urgencyReason: '',
      additionalNotes: '',
    },
    validationSchema,
    onSubmit: async (values) => {
      await handleSubmit(values, false);
    },
  });

  useEffect(() => {
    if (selectedPatient) {
      setSelectedPatientData(selectedPatient);
      formik.setFieldValue('patientId', selectedPatient.id);
      if (selectedPatient.insurance.length > 0) {
        const primaryInsurance = selectedPatient.insurance.find(ins => ins.isPrimary) || selectedPatient.insurance[0];
        formik.setFieldValue('insuranceId', primaryInsurance.id);
      }
    }
  }, [selectedPatient]);

  const handleSubmit = async (values: any, saveAsDraft: boolean) => {
    setIsSubmitting(true);
    setIsDraft(saveAsDraft);

    try {
      const formData = new FormData();
      
      // Add form data
      Object.keys(values).forEach(key => {
        if (key === 'diagnosisCodes') {
          formData.append(key, JSON.stringify(values[key].filter((code: string) => code.trim())));
        } else {
          formData.append(key, values[key]);
        }
      });

      // Add metadata
      formData.append('providerId', user?.id || '');
      formData.append('practiceId', user?.practiceId || '');
      formData.append('status', saveAsDraft ? 'DRAFT' : 'SUBMITTED');
      formData.append('trackingId', generateTrackingId());

      // Add documents
      attachedDocuments.forEach((file, index) => {
        formData.append(`documents[${index}]`, file);
      });

      const response = await fetch('/api/prior-auth', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
        },
        body: formData,
      });

      if (!response.ok) {
        throw new Error('Failed to submit prior authorization request');
      }

      const result = await response.json();
      
      if (result.success) {
        onSuccess?.(result.data);
      } else {
        throw new Error(result.error || 'Submission failed');
      }
    } catch (error) {
      console.error('Submission error:', error);
      // Handle error appropriately
    } finally {
      setIsSubmitting(false);
      setIsDraft(false);
    }
  };

  const handleSaveAsDraft = () => {
    handleSubmit(formik.values, true);
  };

  const handleNext = () => {
    setActiveStep((prevActiveStep) => prevActiveStep + 1);
  };

  const handleBack = () => {
    setActiveStep((prevActiveStep) => prevActiveStep - 1);
  };

  const addDiagnosisCode = () => {
    formik.setFieldValue('diagnosisCodes', [...formik.values.diagnosisCodes, '']);
  };

  const removeDiagnosisCode = (index: number) => {
    const newCodes = formik.values.diagnosisCodes.filter((_, i) => i !== index);
    formik.setFieldValue('diagnosisCodes', newCodes);
  };

  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(event.target.files || []);
    setAttachedDocuments(prev => [...prev, ...files]);
  };

  const removeDocument = (index: number) => {
    setAttachedDocuments(prev => prev.filter((_, i) => i !== index));
  };

  const generateTrackingId = (): string => {
    const timestamp = Date.now().toString(36);
    const random = Math.random().toString(36).substr(2, 5);
    return `PA-${timestamp}-${random}`.toUpperCase();
  };

  const renderPatientSelection = () => (
    <Box>
      <Typography variant="h6" sx={{ mb: 2, display: 'flex', alignItems: 'center' }}>
        <SecurityIcon sx={{ mr: 1 }} />
        Patient Selection
      </Typography>
      
      {selectedPatientData ? (
        <Card sx={{ mb: 2, bgcolor: 'success.light' }}>
          <CardContent>
            <Typography variant="h6">
              {selectedPatientData.lastName}, {selectedPatientData.firstName}
            </Typography>
            <Typography variant="body2" color="text.secondary">
              MRN: {selectedPatientData.mrn} • DOB: {new Date(selectedPatientData.dateOfBirth).toLocaleDateString()}
            </Typography>
            <Typography variant="body2" color="text.secondary">
              Insurance: {selectedPatientData.insurance.find(ins => ins.isPrimary)?.insuranceCompany || 'None'}
            </Typography>
            <Button
              variant="outlined"
              size="small"
              onClick={() => setSelectedPatientData(null)}
              sx={{ mt: 1 }}
            >
              Change Patient
            </Button>
          </CardContent>
        </Card>
      ) : (
        <PatientSearch
          onPatientSelect={(patient) => {
            setSelectedPatientData(patient);
            formik.setFieldValue('patientId', patient.id);
            if (patient.insurance.length > 0) {
              const primaryInsurance = patient.insurance.find(ins => ins.isPrimary) || patient.insurance[0];
              formik.setFieldValue('insuranceId', primaryInsurance.id);
            }
          }}
          placeholder="Search for patient to create prior authorization..."
        />
      )}

      {selectedPatientData && selectedPatientData.insurance.length > 0 && (
        <FormControl fullWidth sx={{ mt: 2 }}>
          <InputLabel>Select Insurance</InputLabel>
          <Select
            name="insuranceId"
            value={formik.values.insuranceId}
            onChange={formik.handleChange}
            label="Select Insurance"
          >
            {selectedPatientData.insurance.map((insurance) => (
              <MenuItem key={insurance.id} value={insurance.id}>
                {insurance.insuranceCompany} - {insurance.policyNumber}
                {insurance.isPrimary && <Chip label="Primary" size="small" sx={{ ml: 1 }} />}
              </MenuItem>
            ))}
          </Select>
        </FormControl>
      )}
    </Box>
  );

  const renderProcedureInformation = () => (
    <Box>
      <Typography variant="h6" sx={{ mb: 2, display: 'flex', alignItems: 'center' }}>
        <MedicalIcon sx={{ mr: 1 }} />
        Procedure Information
      </Typography>
      
      <Grid container spacing={3}>
        <Grid item xs={12} sm={6}>
          <TextField
            fullWidth
            name="procedureCode"
            label="Procedure Code (CPT/HCPCS) *"
            value={formik.values.procedureCode}
            onChange={formik.handleChange}
            onBlur={formik.handleBlur}
            error={formik.touched.procedureCode && Boolean(formik.errors.procedureCode)}
            helperText={formik.touched.procedureCode && formik.errors.procedureCode}
            InputProps={{
              endAdornment: (
                <InputAdornment position="end">
                  <IconButton>
                    <SearchIcon />
                  </IconButton>
                </InputAdornment>
              ),
            }}
          />
        </Grid>
        
        <Grid item xs={12} sm={6}>
          <FormControl fullWidth>
            <InputLabel>Priority *</InputLabel>
            <Select
              name="priority"
              value={formik.values.priority}
              onChange={formik.handleChange}
              label="Priority *"
            >
              <MenuItem value={Priority.ROUTINE}>Routine</MenuItem>
              <MenuItem value={Priority.URGENT}>Urgent</MenuItem>
              <MenuItem value={Priority.STAT}>STAT</MenuItem>
            </Select>
          </FormControl>
        </Grid>
        
        <Grid item xs={12}>
          <TextField
            fullWidth
            name="procedureDescription"
            label="Procedure Description *"
            multiline
            rows={3}
            value={formik.values.procedureDescription}
            onChange={formik.handleChange}
            onBlur={formik.handleBlur}
            error={formik.touched.procedureDescription && Boolean(formik.errors.procedureDescription)}
            helperText={formik.touched.procedureDescription && formik.errors.procedureDescription}
          />
        </Grid>
        
        <Grid item xs={12}>
          <Typography variant="subtitle1" sx={{ mb: 1 }}>
            Diagnosis Codes (ICD-10) *
          </Typography>
          
          {formik.values.diagnosisCodes.map((code, index) => (
            <Box key={index} sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
              <TextField
                fullWidth
                label={`Diagnosis Code ${index + 1}`}
                value={code}
                onChange={(e) => {
                  const newCodes = [...formik.values.diagnosisCodes];
                  newCodes[index] = e.target.value;
                  formik.setFieldValue('diagnosisCodes', newCodes);
                }}
                InputProps={{
                  endAdornment: (
                    <InputAdornment position="end">
                      <IconButton>
                        <SearchIcon />
                      </IconButton>
                    </InputAdornment>
                  ),
                }}
              />
              
              {formik.values.diagnosisCodes.length > 1 && (
                <IconButton
                  onClick={() => removeDiagnosisCode(index)}
                  sx={{ ml: 1 }}
                  color="error"
                >
                  <RemoveIcon />
                </IconButton>
              )}
            </Box>
          ))}
          
          <Button
            variant="outlined"
            startIcon={<AddIcon />}
            onClick={addDiagnosisCode}
            sx={{ mt: 1 }}
          >
            Add Diagnosis Code
          </Button>
        </Grid>
        
        <Grid item xs={12}>
          <TextField
            fullWidth
            name="requestedDate"
            label="Requested Service Date"
            type="date"
            value={formik.values.requestedDate}
            onChange={formik.handleChange}
            InputLabelProps={{ shrink: true }}
          />
        </Grid>
        
        {formik.values.priority !== Priority.ROUTINE && (
          <Grid item xs={12}>
            <TextField
              fullWidth
              name="urgencyReason"
              label="Reason for Urgency"
              multiline
              rows={2}
              value={formik.values.urgencyReason}
              onChange={formik.handleChange}
              helperText="Please explain why this request requires urgent processing"
            />
          </Grid>
        )}
      </Grid>
    </Box>
  );

  return (
    <Card sx={{ maxWidth: 1000, mx: 'auto' }}>
      <CardContent>
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
          <AssignmentIcon sx={{ mr: 1, color: 'primary.main' }} />
          <Typography variant="h5" component="h1">
            Prior Authorization Request
          </Typography>
          <Chip label="HIPAA Compliant" size="small" color="success" sx={{ ml: 'auto' }} />
        </Box>

        <Stepper activeStep={activeStep} orientation="vertical">
          {steps.map((label, index) => (
            <Step key={label}>
              <StepLabel>{label}</StepLabel>
              <StepContent>
                <Box sx={{ mb: 2 }}>
                  {index === 0 && renderPatientSelection()}
                  {index === 1 && renderProcedureInformation()}
                  {/* Additional steps would be implemented here */}
                </Box>
                
                <Box sx={{ mb: 1 }}>
                  <Button
                    variant="contained"
                    onClick={index === steps.length - 1 ? formik.handleSubmit : handleNext}
                    sx={{ mt: 1, mr: 1 }}
                    disabled={isSubmitting || (index === 0 && !selectedPatientData)}
                    startIcon={index === steps.length - 1 ? <SendIcon /> : undefined}
                  >
                    {index === steps.length - 1 ? 'Submit Request' : 'Continue'}
                  </Button>
                  
                  <Button
                    disabled={index === 0}
                    onClick={handleBack}
                    sx={{ mt: 1, mr: 1 }}
                  >
                    Back
                  </Button>
                  
                  <Button
                    onClick={handleSaveAsDraft}
                    sx={{ mt: 1, mr: 1 }}
                    startIcon={<SaveIcon />}
                    disabled={isSubmitting}
                  >
                    Save Draft
                  </Button>
                  
                  <Button
                    onClick={onCancel}
                    sx={{ mt: 1 }}
                    startIcon={<CancelIcon />}
                  >
                    Cancel
                  </Button>
                </Box>
              </StepContent>
            </Step>
          ))}
        </Stepper>
      </CardContent>
    </Card>
  );
};

export default PriorAuthRequestForm;
