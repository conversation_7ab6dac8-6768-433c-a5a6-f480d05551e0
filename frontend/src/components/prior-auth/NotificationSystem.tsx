import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  Card,
  CardContent,
  <PERSON><PERSON><PERSON>,
  Button,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  ListItemSecondaryAction,
  IconButton,
  Chip,
  Badge,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Alert,
  Snackbar,
  Menu,
  MenuItem,
  Divider,
  FormControl,
  InputLabel,
  Select,
  Switch,
  FormControlLabel,
  TextField,
  Grid,
  Tooltip,
} from '@mui/material';
import {
  Notifications as NotificationsIcon,
  CheckCircle as ApprovedIcon,
  Cancel as DeniedIcon,
  Schedule as PendingIcon,
  Warning as WarningIcon,
  Info as InfoIcon,
  Email as EmailIcon,
  Sms as SmsIcon,
  Phone as PhoneIcon,
  Close as CloseIcon,
  Settings as SettingsIcon,
  MarkEmailRead as MarkReadIcon,
  Delete as DeleteIcon,
  Refresh as RefreshIcon,
} from '@mui/icons-material';
import { PriorAuthorization, PriorAuthStatus, Communication } from '../../types';
import { formatDateTime, formatRelativeTime } from '../../utils/formatters';

interface Notification {
  id: string;
  type: 'approval' | 'denial' | 'status_change' | 'deadline' | 'communication' | 'system';
  title: string;
  message: string;
  priorAuthId?: string;
  patientName?: string;
  trackingId?: string;
  priority: 'low' | 'medium' | 'high' | 'urgent';
  isRead: boolean;
  timestamp: Date;
  actionRequired: boolean;
  metadata?: Record<string, any>;
}

interface NotificationPreferences {
  emailEnabled: boolean;
  smsEnabled: boolean;
  inAppEnabled: boolean;
  approvalNotifications: boolean;
  denialNotifications: boolean;
  deadlineReminders: boolean;
  statusUpdates: boolean;
  communicationAlerts: boolean;
  reminderDays: number;
  quietHours: {
    enabled: boolean;
    startTime: string;
    endTime: string;
  };
}

interface NotificationSystemProps {
  onNotificationClick?: (notification: Notification) => void;
  maxDisplayCount?: number;
  autoRefresh?: boolean;
  refreshInterval?: number;
}

const NotificationSystem: React.FC<NotificationSystemProps> = ({
  onNotificationClick,
  maxDisplayCount = 50,
  autoRefresh = true,
  refreshInterval = 30000, // 30 seconds
}) => {
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [unreadCount, setUnreadCount] = useState(0);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const [selectedNotification, setSelectedNotification] = useState<Notification | null>(null);
  const [preferencesDialog, setPreferencesDialog] = useState(false);
  const [preferences, setPreferences] = useState<NotificationPreferences>({
    emailEnabled: true,
    smsEnabled: false,
    inAppEnabled: true,
    approvalNotifications: true,
    denialNotifications: true,
    deadlineReminders: true,
    statusUpdates: true,
    communicationAlerts: true,
    reminderDays: 2,
    quietHours: {
      enabled: false,
      startTime: '22:00',
      endTime: '08:00',
    },
  });
  const [snackbar, setSnackbar] = useState<{
    open: boolean;
    message: string;
    severity: 'success' | 'error' | 'warning' | 'info';
  }>({
    open: false,
    message: '',
    severity: 'info',
  });

  useEffect(() => {
    loadNotifications();
    loadPreferences();

    if (autoRefresh) {
      const interval = setInterval(loadNotifications, refreshInterval);
      return () => clearInterval(interval);
    }
  }, [autoRefresh, refreshInterval]);

  const loadNotifications = async () => {
    setIsLoading(true);
    setError(null);

    try {
      const response = await fetch('/api/notifications', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error('Failed to load notifications');
      }

      const data = await response.json();
      
      if (data.success) {
        const notificationData = data.data || [];
        setNotifications(notificationData.slice(0, maxDisplayCount));
        setUnreadCount(notificationData.filter((n: Notification) => !n.isRead).length);
      } else {
        throw new Error(data.error || 'Failed to load notifications');
      }
    } catch (error) {
      setError(error instanceof Error ? error.message : 'Failed to load notifications');
    } finally {
      setIsLoading(false);
    }
  };

  const loadPreferences = async () => {
    try {
      const response = await fetch('/api/notifications/preferences', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
        },
      });

      if (response.ok) {
        const data = await response.json();
        if (data.success && data.data) {
          setPreferences(data.data);
        }
      }
    } catch (error) {
      console.error('Failed to load notification preferences:', error);
    }
  };

  const markAsRead = async (notificationId: string) => {
    try {
      const response = await fetch(`/api/notifications/${notificationId}/read`, {
        method: 'PUT',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
        },
      });

      if (response.ok) {
        setNotifications(prev => prev.map(n => 
          n.id === notificationId ? { ...n, isRead: true } : n
        ));
        setUnreadCount(prev => Math.max(0, prev - 1));
      }
    } catch (error) {
      console.error('Failed to mark notification as read:', error);
    }
  };

  const markAllAsRead = async () => {
    try {
      const response = await fetch('/api/notifications/mark-all-read', {
        method: 'PUT',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
        },
      });

      if (response.ok) {
        setNotifications(prev => prev.map(n => ({ ...n, isRead: true })));
        setUnreadCount(0);
        showSnackbar('All notifications marked as read', 'success');
      }
    } catch (error) {
      console.error('Failed to mark all notifications as read:', error);
      showSnackbar('Failed to mark notifications as read', 'error');
    }
  };

  const deleteNotification = async (notificationId: string) => {
    try {
      const response = await fetch(`/api/notifications/${notificationId}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
        },
      });

      if (response.ok) {
        setNotifications(prev => prev.filter(n => n.id !== notificationId));
        showSnackbar('Notification deleted', 'success');
      }
    } catch (error) {
      console.error('Failed to delete notification:', error);
      showSnackbar('Failed to delete notification', 'error');
    }
  };

  const savePreferences = async () => {
    try {
      const response = await fetch('/api/notifications/preferences', {
        method: 'PUT',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(preferences),
      });

      if (response.ok) {
        setPreferencesDialog(false);
        showSnackbar('Notification preferences saved', 'success');
      } else {
        throw new Error('Failed to save preferences');
      }
    } catch (error) {
      showSnackbar('Failed to save preferences', 'error');
    }
  };

  const showSnackbar = (message: string, severity: 'success' | 'error' | 'warning' | 'info') => {
    setSnackbar({ open: true, message, severity });
  };

  const handleNotificationClick = (notification: Notification) => {
    if (!notification.isRead) {
      markAsRead(notification.id);
    }
    
    setSelectedNotification(notification);
    onNotificationClick?.(notification);
    setAnchorEl(null);
  };

  const getNotificationIcon = (type: Notification['type'], priority: Notification['priority']) => {
    const iconProps = {
      color: priority === 'urgent' ? 'error' as const : 
             priority === 'high' ? 'warning' as const : 
             'primary' as const
    };

    switch (type) {
      case 'approval':
        return <ApprovedIcon {...iconProps} />;
      case 'denial':
        return <DeniedIcon color="error" />;
      case 'deadline':
        return <WarningIcon color="warning" />;
      case 'communication':
        return <EmailIcon {...iconProps} />;
      case 'status_change':
        return <InfoIcon {...iconProps} />;
      default:
        return <NotificationsIcon {...iconProps} />;
    }
  };

  const getPriorityChip = (priority: Notification['priority']) => {
    const chipProps = {
      urgent: { label: 'URGENT', color: 'error' as const },
      high: { label: 'HIGH', color: 'warning' as const },
      medium: { label: 'MEDIUM', color: 'info' as const },
      low: { label: 'LOW', color: 'default' as const },
    };

    return <Chip size="small" {...chipProps[priority]} />;
  };

  return (
    <>
      {/* Notification Bell Icon */}
      <Tooltip title="Notifications">
        <IconButton
          onClick={(e) => setAnchorEl(e.currentTarget)}
          color="inherit"
        >
          <Badge badgeContent={unreadCount} color="error">
            <NotificationsIcon />
          </Badge>
        </IconButton>
      </Tooltip>

      {/* Notification Menu */}
      <Menu
        anchorEl={anchorEl}
        open={Boolean(anchorEl)}
        onClose={() => setAnchorEl(null)}
        PaperProps={{
          sx: { width: 400, maxHeight: 600 }
        }}
      >
        <Box sx={{ p: 2, display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
          <Typography variant="h6">Notifications</Typography>
          <Box>
            <IconButton size="small" onClick={loadNotifications} disabled={isLoading}>
              <RefreshIcon />
            </IconButton>
            <IconButton size="small" onClick={() => setPreferencesDialog(true)}>
              <SettingsIcon />
            </IconButton>
          </Box>
        </Box>

        <Divider />

        {unreadCount > 0 && (
          <MenuItem onClick={markAllAsRead}>
            <ListItemIcon>
              <MarkReadIcon />
            </ListItemIcon>
            <ListItemText primary="Mark all as read" />
          </MenuItem>
        )}

        {error && (
          <Box sx={{ p: 2 }}>
            <Alert severity="error">{error}</Alert>
          </Box>
        )}

        {notifications.length === 0 ? (
          <Box sx={{ p: 3, textAlign: 'center' }}>
            <Typography variant="body2" color="text.secondary">
              No notifications
            </Typography>
          </Box>
        ) : (
          <List sx={{ maxHeight: 400, overflow: 'auto' }}>
            {notifications.map((notification) => (
              <MenuItem
                key={notification.id}
                onClick={() => handleNotificationClick(notification)}
                sx={{
                  bgcolor: notification.isRead ? 'transparent' : 'action.hover',
                  borderLeft: notification.isRead ? 'none' : '4px solid',
                  borderLeftColor: notification.priority === 'urgent' ? 'error.main' : 
                                  notification.priority === 'high' ? 'warning.main' : 'primary.main',
                }}
              >
                <ListItemIcon>
                  {getNotificationIcon(notification.type, notification.priority)}
                </ListItemIcon>
                
                <ListItemText
                  primary={
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      <Typography variant="subtitle2" noWrap>
                        {notification.title}
                      </Typography>
                      {getPriorityChip(notification.priority)}
                    </Box>
                  }
                  secondary={
                    <Box>
                      <Typography variant="body2" color="text.secondary" noWrap>
                        {notification.message}
                      </Typography>
                      <Typography variant="caption" color="text.secondary">
                        {formatRelativeTime(notification.timestamp)}
                      </Typography>
                      {notification.trackingId && (
                        <Typography variant="caption" color="primary" sx={{ ml: 1 }}>
                          {notification.trackingId}
                        </Typography>
                      )}
                    </Box>
                  }
                />
                
                <ListItemSecondaryAction>
                  <IconButton
                    size="small"
                    onClick={(e) => {
                      e.stopPropagation();
                      deleteNotification(notification.id);
                    }}
                  >
                    <DeleteIcon fontSize="small" />
                  </IconButton>
                </ListItemSecondaryAction>
              </MenuItem>
            ))}
          </List>
        )}
      </Menu>

      {/* Notification Details Dialog */}
      <Dialog
        open={!!selectedNotification}
        onClose={() => setSelectedNotification(null)}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            {selectedNotification && getNotificationIcon(selectedNotification.type, selectedNotification.priority)}
            <Typography variant="h6">
              {selectedNotification?.title}
            </Typography>
            {selectedNotification && getPriorityChip(selectedNotification.priority)}
          </Box>
        </DialogTitle>
        
        <DialogContent>
          {selectedNotification && (
            <Box>
              <Typography variant="body1" sx={{ mb: 2 }}>
                {selectedNotification.message}
              </Typography>
              
              <Grid container spacing={2}>
                <Grid item xs={6}>
                  <Typography variant="caption" color="text.secondary">
                    Timestamp
                  </Typography>
                  <Typography variant="body2">
                    {formatDateTime(selectedNotification.timestamp)}
                  </Typography>
                </Grid>
                
                {selectedNotification.trackingId && (
                  <Grid item xs={6}>
                    <Typography variant="caption" color="text.secondary">
                      Tracking ID
                    </Typography>
                    <Typography variant="body2">
                      {selectedNotification.trackingId}
                    </Typography>
                  </Grid>
                )}
                
                {selectedNotification.patientName && (
                  <Grid item xs={6}>
                    <Typography variant="caption" color="text.secondary">
                      Patient
                    </Typography>
                    <Typography variant="body2">
                      {selectedNotification.patientName}
                    </Typography>
                  </Grid>
                )}
                
                <Grid item xs={6}>
                  <Typography variant="caption" color="text.secondary">
                    Action Required
                  </Typography>
                  <Typography variant="body2">
                    {selectedNotification.actionRequired ? 'Yes' : 'No'}
                  </Typography>
                </Grid>
              </Grid>
              
              {selectedNotification.metadata && Object.keys(selectedNotification.metadata).length > 0 && (
                <Box sx={{ mt: 2 }}>
                  <Typography variant="subtitle2" sx={{ mb: 1 }}>
                    Additional Information
                  </Typography>
                  {Object.entries(selectedNotification.metadata).map(([key, value]) => (
                    <Typography key={key} variant="body2" sx={{ mb: 0.5 }}>
                      <strong>{key}:</strong> {String(value)}
                    </Typography>
                  ))}
                </Box>
              )}
            </Box>
          )}
        </DialogContent>
        
        <DialogActions>
          <Button onClick={() => setSelectedNotification(null)}>Close</Button>
          {selectedNotification?.actionRequired && (
            <Button variant="contained" color="primary">
              Take Action
            </Button>
          )}
        </DialogActions>
      </Dialog>

      {/* Preferences Dialog */}
      <Dialog
        open={preferencesDialog}
        onClose={() => setPreferencesDialog(false)}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle>Notification Preferences</DialogTitle>
        
        <DialogContent>
          <Grid container spacing={3} sx={{ mt: 1 }}>
            <Grid item xs={12}>
              <Typography variant="subtitle1" sx={{ mb: 2 }}>
                Delivery Methods
              </Typography>
              
              <FormControlLabel
                control={
                  <Switch
                    checked={preferences.emailEnabled}
                    onChange={(e) => setPreferences(prev => ({ ...prev, emailEnabled: e.target.checked }))}
                  />
                }
                label="Email Notifications"
              />
              
              <FormControlLabel
                control={
                  <Switch
                    checked={preferences.smsEnabled}
                    onChange={(e) => setPreferences(prev => ({ ...prev, smsEnabled: e.target.checked }))}
                  />
                }
                label="SMS Notifications"
              />
              
              <FormControlLabel
                control={
                  <Switch
                    checked={preferences.inAppEnabled}
                    onChange={(e) => setPreferences(prev => ({ ...prev, inAppEnabled: e.target.checked }))}
                  />
                }
                label="In-App Notifications"
              />
            </Grid>
            
            <Grid item xs={12}>
              <Typography variant="subtitle1" sx={{ mb: 2 }}>
                Notification Types
              </Typography>
              
              <FormControlLabel
                control={
                  <Switch
                    checked={preferences.approvalNotifications}
                    onChange={(e) => setPreferences(prev => ({ ...prev, approvalNotifications: e.target.checked }))}
                  />
                }
                label="Approval Notifications"
              />
              
              <FormControlLabel
                control={
                  <Switch
                    checked={preferences.denialNotifications}
                    onChange={(e) => setPreferences(prev => ({ ...prev, denialNotifications: e.target.checked }))}
                  />
                }
                label="Denial Notifications"
              />
              
              <FormControlLabel
                control={
                  <Switch
                    checked={preferences.deadlineReminders}
                    onChange={(e) => setPreferences(prev => ({ ...prev, deadlineReminders: e.target.checked }))}
                  />
                }
                label="Deadline Reminders"
              />
              
              <FormControlLabel
                control={
                  <Switch
                    checked={preferences.statusUpdates}
                    onChange={(e) => setPreferences(prev => ({ ...prev, statusUpdates: e.target.checked }))}
                  />
                }
                label="Status Updates"
              />
            </Grid>
            
            <Grid item xs={12}>
              <TextField
                fullWidth
                type="number"
                label="Reminder Days Before Deadline"
                value={preferences.reminderDays}
                onChange={(e) => setPreferences(prev => ({ ...prev, reminderDays: parseInt(e.target.value) || 2 }))}
                inputProps={{ min: 1, max: 30 }}
              />
            </Grid>
          </Grid>
        </DialogContent>
        
        <DialogActions>
          <Button onClick={() => setPreferencesDialog(false)}>Cancel</Button>
          <Button variant="contained" onClick={savePreferences}>
            Save Preferences
          </Button>
        </DialogActions>
      </Dialog>

      {/* Snackbar */}
      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={() => setSnackbar(prev => ({ ...prev, open: false }))}
      >
        <Alert
          onClose={() => setSnackbar(prev => ({ ...prev, open: false }))}
          severity={snackbar.severity}
          sx={{ width: '100%' }}
        >
          {snackbar.message}
        </Alert>
      </Snackbar>
    </>
  );
};

export default NotificationSystem;
