import React, { useState, useCallback, useRef } from 'react';
import {
  <PERSON>,
  Card,
  CardContent,
  <PERSON><PERSON><PERSON>,
  Button,
  LinearProgress,
  Alert,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  ListItemSecondaryAction,
  IconButton,
  Chip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  TextField,
  Tooltip,
  Grid,
} from '@mui/material';
import {
  CloudUpload as UploadIcon,
  Delete as DeleteIcon,
  InsertDriveFile as FileIcon,
  Security as SecurityIcon,
  CheckCircle as CheckCircleIcon,
  Error as ErrorIcon,
  Warning as WarningIcon,
  Visibility as VisibilityIcon,
  GetApp as DownloadIcon,
} from '@mui/icons-material';
import { useDropzone } from 'react-dropzone';
import { DocumentType } from '../../types';
import { formatFileSize } from '../../utils/formatters';

interface UploadedFile {
  id: string;
  file: File;
  progress: number;
  status: 'uploading' | 'completed' | 'error' | 'encrypted';
  error?: string;
  documentType?: DocumentType;
  description?: string;
  encryptionStatus?: 'pending' | 'encrypted' | 'failed';
}

interface SecureFileUploadProps {
  onFilesUploaded?: (files: UploadedFile[]) => void;
  maxFiles?: number;
  maxFileSize?: number; // in bytes
  acceptedFileTypes?: string[];
  requireEncryption?: boolean;
  patientId?: string;
  priorAuthId?: string;
  disabled?: boolean;
}

const SecureFileUpload: React.FC<SecureFileUploadProps> = ({
  onFilesUploaded,
  maxFiles = 10,
  maxFileSize = 10 * 1024 * 1024, // 10MB default
  acceptedFileTypes = ['.pdf', '.jpg', '.jpeg', '.png', '.doc', '.docx'],
  requireEncryption = true,
  patientId,
  priorAuthId,
  disabled = false,
}) => {
  const [uploadedFiles, setUploadedFiles] = useState<UploadedFile[]>([]);
  const [isUploading, setIsUploading] = useState(false);
  const [uploadError, setUploadError] = useState<string | null>(null);
  const [documentTypeDialog, setDocumentTypeDialog] = useState<string | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const onDrop = useCallback((acceptedFiles: File[], rejectedFiles: any[]) => {
    setUploadError(null);

    // Handle rejected files
    if (rejectedFiles.length > 0) {
      const errors = rejectedFiles.map(rejection => 
        `${rejection.file.name}: ${rejection.errors.map((e: any) => e.message).join(', ')}`
      );
      setUploadError(`Some files were rejected: ${errors.join('; ')}`);
    }

    // Check total file count
    if (uploadedFiles.length + acceptedFiles.length > maxFiles) {
      setUploadError(`Maximum ${maxFiles} files allowed`);
      return;
    }

    // Process accepted files
    const newFiles: UploadedFile[] = acceptedFiles.map(file => ({
      id: generateFileId(),
      file,
      progress: 0,
      status: 'uploading',
      encryptionStatus: requireEncryption ? 'pending' : undefined,
    }));

    setUploadedFiles(prev => [...prev, ...newFiles]);
    
    // Start uploading files
    newFiles.forEach(uploadFile);
  }, [uploadedFiles, maxFiles, requireEncryption]);

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: acceptedFileTypes.reduce((acc, type) => {
      acc[type] = [];
      return acc;
    }, {} as Record<string, string[]>),
    maxSize: maxFileSize,
    disabled: disabled || isUploading,
  });

  const generateFileId = (): string => {
    return `file_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  };

  const uploadFile = async (fileData: UploadedFile) => {
    setIsUploading(true);

    try {
      const formData = new FormData();
      formData.append('file', fileData.file);
      formData.append('documentType', fileData.documentType || DocumentType.OTHER);
      formData.append('description', fileData.description || '');
      formData.append('requireEncryption', requireEncryption.toString());
      
      if (patientId) formData.append('patientId', patientId);
      if (priorAuthId) formData.append('priorAuthId', priorAuthId);

      const xhr = new XMLHttpRequest();

      // Track upload progress
      xhr.upload.addEventListener('progress', (event) => {
        if (event.lengthComputable) {
          const progress = Math.round((event.loaded / event.total) * 100);
          updateFileProgress(fileData.id, progress);
        }
      });

      // Handle completion
      xhr.addEventListener('load', () => {
        if (xhr.status === 200) {
          const response = JSON.parse(xhr.responseText);
          if (response.success) {
            updateFileStatus(fileData.id, 'completed');
            if (requireEncryption) {
              // Start encryption process
              encryptFile(fileData.id, response.data.fileId);
            }
          } else {
            updateFileStatus(fileData.id, 'error', response.error || 'Upload failed');
          }
        } else {
          updateFileStatus(fileData.id, 'error', `Upload failed with status ${xhr.status}`);
        }
      });

      // Handle errors
      xhr.addEventListener('error', () => {
        updateFileStatus(fileData.id, 'error', 'Network error during upload');
      });

      // Start upload
      xhr.open('POST', '/api/documents/upload');
      xhr.setRequestHeader('Authorization', `Bearer ${localStorage.getItem('token')}`);
      xhr.send(formData);

    } catch (error) {
      updateFileStatus(fileData.id, 'error', error instanceof Error ? error.message : 'Upload failed');
    } finally {
      setIsUploading(false);
    }
  };

  const encryptFile = async (fileId: string, serverFileId: string) => {
    try {
      updateFileEncryptionStatus(fileId, 'pending');

      const response = await fetch(`/api/documents/${serverFileId}/encrypt`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
          'Content-Type': 'application/json',
        },
      });

      if (response.ok) {
        const result = await response.json();
        if (result.success) {
          updateFileEncryptionStatus(fileId, 'encrypted');
          updateFileStatus(fileId, 'encrypted');
        } else {
          updateFileEncryptionStatus(fileId, 'failed');
        }
      } else {
        updateFileEncryptionStatus(fileId, 'failed');
      }
    } catch (error) {
      updateFileEncryptionStatus(fileId, 'failed');
    }
  };

  const updateFileProgress = (fileId: string, progress: number) => {
    setUploadedFiles(prev => prev.map(file => 
      file.id === fileId ? { ...file, progress } : file
    ));
  };

  const updateFileStatus = (fileId: string, status: UploadedFile['status'], error?: string) => {
    setUploadedFiles(prev => prev.map(file => 
      file.id === fileId ? { ...file, status, error } : file
    ));
  };

  const updateFileEncryptionStatus = (fileId: string, encryptionStatus: UploadedFile['encryptionStatus']) => {
    setUploadedFiles(prev => prev.map(file => 
      file.id === fileId ? { ...file, encryptionStatus } : file
    ));
  };

  const removeFile = (fileId: string) => {
    setUploadedFiles(prev => prev.filter(file => file.id !== fileId));
  };

  const setDocumentType = (fileId: string, documentType: DocumentType, description: string) => {
    setUploadedFiles(prev => prev.map(file => 
      file.id === fileId ? { ...file, documentType, description } : file
    ));
    setDocumentTypeDialog(null);
  };

  const getFileIcon = (fileName: string) => {
    const extension = fileName.split('.').pop()?.toLowerCase();
    // Return appropriate icon based on file type
    return <FileIcon />;
  };

  const getStatusIcon = (file: UploadedFile) => {
    switch (file.status) {
      case 'completed':
        return <CheckCircleIcon color="success" />;
      case 'encrypted':
        return <SecurityIcon color="primary" />;
      case 'error':
        return <ErrorIcon color="error" />;
      default:
        return null;
    }
  };

  const getStatusChip = (file: UploadedFile) => {
    if (file.status === 'uploading') {
      return <Chip label="Uploading..." size="small" color="info" />;
    }
    if (file.status === 'encrypted') {
      return <Chip label="Encrypted" size="small" color="success" icon={<SecurityIcon />} />;
    }
    if (file.status === 'completed') {
      return <Chip label="Uploaded" size="small" color="success" />;
    }
    if (file.status === 'error') {
      return <Chip label="Error" size="small" color="error" />;
    }
    return null;
  };

  return (
    <Card>
      <CardContent>
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
          <SecurityIcon sx={{ mr: 1, color: 'primary.main' }} />
          <Typography variant="h6" component="h2">
            Secure File Upload
          </Typography>
          {requireEncryption && (
            <Chip label="HIPAA Encrypted" size="small" color="success" sx={{ ml: 'auto' }} />
          )}
        </Box>

        {uploadError && (
          <Alert severity="error" sx={{ mb: 2 }} onClose={() => setUploadError(null)}>
            {uploadError}
          </Alert>
        )}

        {/* Upload Area */}
        <Box
          {...getRootProps()}
          sx={{
            border: '2px dashed',
            borderColor: isDragActive ? 'primary.main' : 'grey.300',
            borderRadius: 2,
            p: 3,
            textAlign: 'center',
            cursor: disabled ? 'not-allowed' : 'pointer',
            bgcolor: isDragActive ? 'primary.light' : 'grey.50',
            mb: 2,
            opacity: disabled ? 0.5 : 1,
          }}
        >
          <input {...getInputProps()} ref={fileInputRef} />
          <UploadIcon sx={{ fontSize: 48, color: 'grey.400', mb: 1 }} />
          <Typography variant="h6" color="text.secondary">
            {isDragActive ? 'Drop files here' : 'Drag & drop files here'}
          </Typography>
          <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
            or click to select files
          </Typography>
          <Typography variant="caption" color="text.secondary">
            Accepted: {acceptedFileTypes.join(', ')} • Max size: {formatFileSize(maxFileSize)} • Max files: {maxFiles}
          </Typography>
          {requireEncryption && (
            <Typography variant="caption" color="primary" sx={{ display: 'block', mt: 1 }}>
              All files will be encrypted for HIPAA compliance
            </Typography>
          )}
        </Box>

        {/* File List */}
        {uploadedFiles.length > 0 && (
          <Box>
            <Typography variant="subtitle1" sx={{ mb: 1 }}>
              Files ({uploadedFiles.length}/{maxFiles})
            </Typography>
            
            <List>
              {uploadedFiles.map((file) => (
                <ListItem key={file.id} divider>
                  <ListItemIcon>
                    {getFileIcon(file.file.name)}
                  </ListItemIcon>
                  
                  <ListItemText
                    primary={
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                        <Typography variant="body1">
                          {file.file.name}
                        </Typography>
                        {getStatusChip(file)}
                      </Box>
                    }
                    secondary={
                      <Box>
                        <Typography variant="body2" color="text.secondary">
                          {formatFileSize(file.file.size)} • {file.documentType || 'Type not set'}
                        </Typography>
                        
                        {file.status === 'uploading' && (
                          <LinearProgress 
                            variant="determinate" 
                            value={file.progress} 
                            sx={{ mt: 1 }}
                          />
                        )}
                        
                        {file.error && (
                          <Typography variant="caption" color="error">
                            {file.error}
                          </Typography>
                        )}
                        
                        {file.encryptionStatus === 'pending' && (
                          <Typography variant="caption" color="info.main">
                            Encrypting...
                          </Typography>
                        )}
                      </Box>
                    }
                  />
                  
                  <ListItemSecondaryAction>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      {getStatusIcon(file)}
                      
                      {!file.documentType && file.status === 'completed' && (
                        <Tooltip title="Set document type">
                          <IconButton
                            onClick={() => setDocumentTypeDialog(file.id)}
                            color="warning"
                          >
                            <WarningIcon />
                          </IconButton>
                        </Tooltip>
                      )}
                      
                      <IconButton
                        onClick={() => removeFile(file.id)}
                        color="error"
                        disabled={file.status === 'uploading'}
                      >
                        <DeleteIcon />
                      </IconButton>
                    </Box>
                  </ListItemSecondaryAction>
                </ListItem>
              ))}
            </List>
          </Box>
        )}

        {/* Document Type Dialog */}
        <Dialog
          open={!!documentTypeDialog}
          onClose={() => setDocumentTypeDialog(null)}
          maxWidth="sm"
          fullWidth
        >
          <DialogTitle>Set Document Type</DialogTitle>
          <DialogContent>
            <Grid container spacing={2} sx={{ mt: 1 }}>
              <Grid item xs={12}>
                <FormControl fullWidth>
                  <InputLabel>Document Type</InputLabel>
                  <Select
                    defaultValue=""
                    label="Document Type"
                  >
                    {Object.values(DocumentType).map((type) => (
                      <MenuItem key={type} value={type}>
                        {type.replace(/_/g, ' ')}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Grid>
              
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label="Description (Optional)"
                  multiline
                  rows={3}
                  placeholder="Brief description of the document..."
                />
              </Grid>
            </Grid>
          </DialogContent>
          <DialogActions>
            <Button onClick={() => setDocumentTypeDialog(null)}>Cancel</Button>
            <Button variant="contained">Save</Button>
          </DialogActions>
        </Dialog>
      </CardContent>
    </Card>
  );
};

export default SecureFileUpload;
