import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  Card,
  CardContent,
  Typography,
  Timeline,
  TimelineItem,
  TimelineSeparator,
  TimelineConnector,
  TimelineContent,
  TimelineDot,
  TimelineOppositeContent,
  Chip,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Divider,
  Alert,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  TextField,
  IconButton,
  Tooltip,
  Collapse,
} from '@mui/material';
import {
  Assignment as AssignmentIcon,
  LocalHospital as MedicalIcon,
  AttachFile as DocumentIcon,
  Phone as PhoneIcon,
  Email as EmailIcon,
  Event as EventIcon,
  Security as SecurityIcon,
  ExpandMore as ExpandMoreIcon,
  ExpandLess as ExpandLessIcon,
  Visibility as VisibilityIcon,
  Add as AddIcon,
  FilterList as FilterIcon,
} from '@mui/icons-material';
import { Patient, PriorAuthorization, Communication, Document } from '../../types';
import { formatDate, formatDateTime, formatRelativeTime } from '../../utils/formatters';

interface TimelineEvent {
  id: string;
  type: 'prior_auth' | 'communication' | 'document' | 'appointment' | 'note' | 'system';
  title: string;
  description: string;
  timestamp: Date;
  status?: string;
  priority?: string;
  relatedId?: string;
  metadata?: Record<string, any>;
  icon: React.ReactNode;
  color: 'primary' | 'secondary' | 'success' | 'warning' | 'error' | 'info';
}

interface PatientTimelineProps {
  patient: Patient;
  priorAuths?: PriorAuthorization[];
  showFilters?: boolean;
  maxEvents?: number;
  compact?: boolean;
}

const PatientTimeline: React.FC<PatientTimelineProps> = ({
  patient,
  priorAuths = [],
  showFilters = true,
  maxEvents = 50,
  compact = false,
}) => {
  const [timelineEvents, setTimelineEvents] = useState<TimelineEvent[]>([]);
  const [filteredEvents, setFilteredEvents] = useState<TimelineEvent[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedEvent, setSelectedEvent] = useState<TimelineEvent | null>(null);
  const [expandedEvents, setExpandedEvents] = useState<Set<string>>(new Set());
  
  // Filters
  const [eventTypeFilter, setEventTypeFilter] = useState<string>('all');
  const [dateRangeFilter, setDateRangeFilter] = useState<string>('all');
  const [statusFilter, setStatusFilter] = useState<string>('all');

  useEffect(() => {
    loadTimelineEvents();
  }, [patient.id]);

  useEffect(() => {
    applyFilters();
  }, [timelineEvents, eventTypeFilter, dateRangeFilter, statusFilter]);

  const loadTimelineEvents = async () => {
    setIsLoading(true);
    setError(null);

    try {
      const response = await fetch(`/api/patients/${patient.id}/timeline`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error('Failed to load patient timeline');
      }

      const data = await response.json();
      
      if (data.success) {
        const events = processTimelineData(data.data);
        setTimelineEvents(events);
      } else {
        throw new Error(data.error || 'Failed to load timeline');
      }
    } catch (error) {
      setError(error instanceof Error ? error.message : 'Failed to load timeline');
    } finally {
      setIsLoading(false);
    }
  };

  const processTimelineData = (data: any): TimelineEvent[] => {
    const events: TimelineEvent[] = [];

    // Add prior authorization events
    priorAuths.forEach(priorAuth => {
      events.push({
        id: `pa_${priorAuth.id}`,
        type: 'prior_auth',
        title: 'Prior Authorization Request',
        description: `${priorAuth.procedureDescription} (${priorAuth.procedureCode})`,
        timestamp: priorAuth.createdAt,
        status: priorAuth.status,
        priority: priorAuth.priority,
        relatedId: priorAuth.id,
        metadata: { trackingId: priorAuth.trackingId },
        icon: <AssignmentIcon />,
        color: getStatusColor(priorAuth.status),
      });

      // Add status change events
      if (priorAuth.submittedDate) {
        events.push({
          id: `pa_submitted_${priorAuth.id}`,
          type: 'prior_auth',
          title: 'Prior Auth Submitted',
          description: `Request submitted to ${priorAuth.insuranceId}`,
          timestamp: priorAuth.submittedDate,
          status: 'submitted',
          relatedId: priorAuth.id,
          icon: <AssignmentIcon />,
          color: 'info',
        });
      }

      if (priorAuth.approvedDate) {
        events.push({
          id: `pa_approved_${priorAuth.id}`,
          type: 'prior_auth',
          title: 'Prior Auth Approved',
          description: `Authorization #${priorAuth.authorizationNumber}`,
          timestamp: priorAuth.approvedDate,
          status: 'approved',
          relatedId: priorAuth.id,
          icon: <AssignmentIcon />,
          color: 'success',
        });
      }

      if (priorAuth.deniedDate) {
        events.push({
          id: `pa_denied_${priorAuth.id}`,
          type: 'prior_auth',
          title: 'Prior Auth Denied',
          description: priorAuth.denialReason || 'Request denied',
          timestamp: priorAuth.deniedDate,
          status: 'denied',
          relatedId: priorAuth.id,
          icon: <AssignmentIcon />,
          color: 'error',
        });
      }

      // Add communication events
      priorAuth.communications.forEach(comm => {
        events.push({
          id: `comm_${comm.id}`,
          type: 'communication',
          title: `${comm.type} - ${comm.direction}`,
          description: comm.subject,
          timestamp: comm.createdAt,
          relatedId: priorAuth.id,
          metadata: { 
            content: comm.content,
            contactName: comm.contactName,
            contactPhone: comm.contactPhone,
          },
          icon: comm.type === 'PHONE_CALL' ? <PhoneIcon /> : <EmailIcon />,
          color: 'secondary',
        });
      });

      // Add document events
      priorAuth.documents.forEach(doc => {
        events.push({
          id: `doc_${doc.id}`,
          type: 'document',
          title: 'Document Added',
          description: `${doc.fileName} (${doc.documentType})`,
          timestamp: doc.uploadedAt,
          relatedId: priorAuth.id,
          metadata: {
            fileSize: doc.fileSize,
            uploadedBy: doc.uploadedBy,
            isEncrypted: doc.isEncrypted,
          },
          icon: <DocumentIcon />,
          color: 'info',
        });
      });
    });

    // Add system events from API data
    if (data.systemEvents) {
      data.systemEvents.forEach((event: any) => {
        events.push({
          id: `sys_${event.id}`,
          type: 'system',
          title: event.title,
          description: event.description,
          timestamp: new Date(event.timestamp),
          metadata: event.metadata,
          icon: <SecurityIcon />,
          color: 'secondary',
        });
      });
    }

    // Sort by timestamp (newest first)
    return events.sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime());
  };

  const applyFilters = () => {
    let filtered = [...timelineEvents];

    // Filter by event type
    if (eventTypeFilter !== 'all') {
      filtered = filtered.filter(event => event.type === eventTypeFilter);
    }

    // Filter by date range
    if (dateRangeFilter !== 'all') {
      const now = new Date();
      let cutoffDate: Date;

      switch (dateRangeFilter) {
        case 'today':
          cutoffDate = new Date(now.getFullYear(), now.getMonth(), now.getDate());
          break;
        case 'week':
          cutoffDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
          break;
        case 'month':
          cutoffDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
          break;
        case 'quarter':
          cutoffDate = new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000);
          break;
        default:
          cutoffDate = new Date(0);
      }

      filtered = filtered.filter(event => event.timestamp >= cutoffDate);
    }

    // Filter by status
    if (statusFilter !== 'all') {
      filtered = filtered.filter(event => event.status === statusFilter);
    }

    // Limit number of events
    filtered = filtered.slice(0, maxEvents);

    setFilteredEvents(filtered);
  };

  const getStatusColor = (status: string): TimelineEvent['color'] => {
    switch (status.toLowerCase()) {
      case 'approved':
        return 'success';
      case 'denied':
        return 'error';
      case 'pending':
      case 'submitted':
      case 'under_review':
        return 'warning';
      case 'draft':
        return 'info';
      default:
        return 'primary';
    }
  };

  const toggleEventExpansion = (eventId: string) => {
    setExpandedEvents(prev => {
      const newSet = new Set(prev);
      if (newSet.has(eventId)) {
        newSet.delete(eventId);
      } else {
        newSet.add(eventId);
      }
      return newSet;
    });
  };

  const renderEventContent = (event: TimelineEvent) => {
    const isExpanded = expandedEvents.has(event.id);

    return (
      <Card sx={{ mb: 1 }}>
        <CardContent sx={{ py: 2 }}>
          <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
            <Box sx={{ flexGrow: 1 }}>
              <Typography variant="subtitle1" sx={{ fontWeight: 'bold' }}>
                {event.title}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                {event.description}
              </Typography>
              
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mt: 1 }}>
                {event.status && (
                  <Chip label={event.status} size="small" color={event.color} />
                )}
                {event.priority && (
                  <Chip label={event.priority} size="small" variant="outlined" />
                )}
                <Typography variant="caption" color="text.secondary">
                  {formatRelativeTime(event.timestamp)}
                </Typography>
              </Box>
            </Box>

            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              {event.metadata && Object.keys(event.metadata).length > 0 && (
                <IconButton
                  size="small"
                  onClick={() => toggleEventExpansion(event.id)}
                >
                  {isExpanded ? <ExpandLessIcon /> : <ExpandMoreIcon />}
                </IconButton>
              )}
              
              <IconButton
                size="small"
                onClick={() => setSelectedEvent(event)}
              >
                <VisibilityIcon />
              </IconButton>
            </Box>
          </Box>

          <Collapse in={isExpanded}>
            <Box sx={{ mt: 2, p: 2, bgcolor: 'grey.50', borderRadius: 1 }}>
              {event.metadata && Object.entries(event.metadata).map(([key, value]) => (
                <Typography key={key} variant="body2" sx={{ mb: 0.5 }}>
                  <strong>{key}:</strong> {String(value)}
                </Typography>
              ))}
            </Box>
          </Collapse>
        </CardContent>
      </Card>
    );
  };

  if (isLoading) {
    return (
      <Card>
        <CardContent>
          <Typography>Loading patient timeline...</Typography>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card>
        <CardContent>
          <Alert severity="error">{error}</Alert>
        </CardContent>
      </Card>
    );
  }

  return (
    <>
      <Card>
        <CardContent>
          <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 3 }}>
            <Typography variant="h6" component="h2">
              Patient Timeline
            </Typography>
            <Chip 
              label={`${filteredEvents.length} events`} 
              size="small" 
              color="primary" 
            />
          </Box>

          {/* Filters */}
          {showFilters && (
            <Box sx={{ display: 'flex', gap: 2, mb: 3, flexWrap: 'wrap' }}>
              <FormControl size="small" sx={{ minWidth: 120 }}>
                <InputLabel>Event Type</InputLabel>
                <Select
                  value={eventTypeFilter}
                  onChange={(e) => setEventTypeFilter(e.target.value)}
                  label="Event Type"
                >
                  <MenuItem value="all">All Types</MenuItem>
                  <MenuItem value="prior_auth">Prior Auth</MenuItem>
                  <MenuItem value="communication">Communication</MenuItem>
                  <MenuItem value="document">Documents</MenuItem>
                  <MenuItem value="system">System</MenuItem>
                </Select>
              </FormControl>

              <FormControl size="small" sx={{ minWidth: 120 }}>
                <InputLabel>Date Range</InputLabel>
                <Select
                  value={dateRangeFilter}
                  onChange={(e) => setDateRangeFilter(e.target.value)}
                  label="Date Range"
                >
                  <MenuItem value="all">All Time</MenuItem>
                  <MenuItem value="today">Today</MenuItem>
                  <MenuItem value="week">Past Week</MenuItem>
                  <MenuItem value="month">Past Month</MenuItem>
                  <MenuItem value="quarter">Past Quarter</MenuItem>
                </Select>
              </FormControl>

              <FormControl size="small" sx={{ minWidth: 120 }}>
                <InputLabel>Status</InputLabel>
                <Select
                  value={statusFilter}
                  onChange={(e) => setStatusFilter(e.target.value)}
                  label="Status"
                >
                  <MenuItem value="all">All Status</MenuItem>
                  <MenuItem value="approved">Approved</MenuItem>
                  <MenuItem value="denied">Denied</MenuItem>
                  <MenuItem value="pending">Pending</MenuItem>
                  <MenuItem value="submitted">Submitted</MenuItem>
                </Select>
              </FormControl>
            </Box>
          )}

          {/* Timeline */}
          {filteredEvents.length > 0 ? (
            <Timeline>
              {filteredEvents.map((event, index) => (
                <TimelineItem key={event.id}>
                  <TimelineOppositeContent sx={{ m: 'auto 0' }} variant="body2" color="text.secondary">
                    {formatDate(event.timestamp)}
                  </TimelineOppositeContent>
                  
                  <TimelineSeparator>
                    <TimelineDot color={event.color}>
                      {event.icon}
                    </TimelineDot>
                    {index < filteredEvents.length - 1 && <TimelineConnector />}
                  </TimelineSeparator>
                  
                  <TimelineContent sx={{ py: '12px', px: 2 }}>
                    {renderEventContent(event)}
                  </TimelineContent>
                </TimelineItem>
              ))}
            </Timeline>
          ) : (
            <Box sx={{ textAlign: 'center', py: 4 }}>
              <Typography variant="body2" color="text.secondary">
                No timeline events found for the selected filters.
              </Typography>
            </Box>
          )}
        </CardContent>
      </Card>

      {/* Event Details Dialog */}
      <Dialog
        open={!!selectedEvent}
        onClose={() => setSelectedEvent(null)}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>Event Details</DialogTitle>
        <DialogContent>
          {selectedEvent && (
            <Box>
              <Typography variant="h6" sx={{ mb: 2 }}>
                {selectedEvent.title}
              </Typography>
              <Typography variant="body1" sx={{ mb: 2 }}>
                {selectedEvent.description}
              </Typography>
              <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                {formatDateTime(selectedEvent.timestamp)}
              </Typography>
              
              {selectedEvent.metadata && (
                <Box sx={{ mt: 2 }}>
                  <Typography variant="subtitle2" sx={{ mb: 1 }}>
                    Additional Information:
                  </Typography>
                  {Object.entries(selectedEvent.metadata).map(([key, value]) => (
                    <Typography key={key} variant="body2" sx={{ mb: 0.5 }}>
                      <strong>{key}:</strong> {String(value)}
                    </Typography>
                  ))}
                </Box>
              )}
            </Box>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setSelectedEvent(null)}>Close</Button>
        </DialogActions>
      </Dialog>
    </>
  );
};

export default PatientTimeline;
