import React, { useState, useEffect } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  Button,
  Grid,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Switch,
  FormControlLabel,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  List,
  ListItem,
  ListItemText,
  ListItemSecondaryAction,
  IconButton,
  Chip,
  Alert,
  Divider,
  Tooltip,
  CircularProgress,
} from '@mui/material';
import {
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Verified as VerifiedIcon,
  Warning as WarningIcon,
  Security as SecurityIcon,
  LocalHospital as InsuranceIcon,
  CheckCircle as CheckCircleIcon,
  Error as ErrorIcon,
  Refresh as RefreshIcon,
} from '@mui/icons-material';
import { useFormik } from 'formik';
import * as Yup from 'yup';
import { InsuranceInfo, Patient } from '../../types';
import { formatDate } from '../../utils/formatters';

interface InsuranceManagementProps {
  patient: Patient;
  onInsuranceUpdate?: (insurance: InsuranceInfo[]) => void;
  readOnly?: boolean;
}

interface InsuranceFormData {
  insuranceCompany: string;
  policyNumber: string;
  groupNumber: string;
  subscriberId: string;
  subscriberName: string;
  effectiveDate: string;
  expirationDate: string;
  isPrimary: boolean;
  relationshipToSubscriber: string;
  copayAmount: string;
  deductibleAmount: string;
  notes: string;
}

interface VerificationStatus {
  isVerified: boolean;
  verificationDate?: Date;
  status: 'active' | 'inactive' | 'pending' | 'error';
  eligibilityDetails?: {
    coverageType: string;
    effectiveDate: Date;
    terminationDate?: Date;
    copayAmount?: number;
    deductibleAmount?: number;
    outOfPocketMax?: number;
  };
  errorMessage?: string;
}

const validationSchema = Yup.object({
  insuranceCompany: Yup.string().required('Insurance company is required'),
  policyNumber: Yup.string().required('Policy number is required'),
  subscriberId: Yup.string().required('Subscriber ID is required'),
  subscriberName: Yup.string().required('Subscriber name is required'),
  effectiveDate: Yup.date().required('Effective date is required'),
  expirationDate: Yup.date().min(
    Yup.ref('effectiveDate'),
    'Expiration date must be after effective date'
  ),
});

const InsuranceManagement: React.FC<InsuranceManagementProps> = ({
  patient,
  onInsuranceUpdate,
  readOnly = false,
}) => {
  const [insuranceList, setInsuranceList] = useState<InsuranceInfo[]>(patient.insurance);
  const [editingInsurance, setEditingInsurance] = useState<InsuranceInfo | null>(null);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [verificationStatuses, setVerificationStatuses] = useState<Record<string, VerificationStatus>>({});
  const [isVerifying, setIsVerifying] = useState<Record<string, boolean>>({});

  const formik = useFormik<InsuranceFormData>({
    initialValues: {
      insuranceCompany: '',
      policyNumber: '',
      groupNumber: '',
      subscriberId: '',
      subscriberName: '',
      effectiveDate: '',
      expirationDate: '',
      isPrimary: false,
      relationshipToSubscriber: 'self',
      copayAmount: '',
      deductibleAmount: '',
      notes: '',
    },
    validationSchema,
    onSubmit: async (values) => {
      await handleSaveInsurance(values);
    },
  });

  useEffect(() => {
    // Load verification statuses for existing insurance
    insuranceList.forEach(insurance => {
      loadVerificationStatus(insurance.id);
    });
  }, [insuranceList]);

  const handleAddInsurance = () => {
    formik.resetForm();
    setEditingInsurance(null);
    setIsDialogOpen(true);
  };

  const handleEditInsurance = (insurance: InsuranceInfo) => {
    setEditingInsurance(insurance);
    formik.setValues({
      insuranceCompany: insurance.insuranceCompany,
      policyNumber: insurance.policyNumber,
      groupNumber: insurance.groupNumber || '',
      subscriberId: insurance.subscriberId,
      subscriberName: insurance.subscriberName,
      effectiveDate: new Date(insurance.effectiveDate).toISOString().split('T')[0],
      expirationDate: insurance.expirationDate 
        ? new Date(insurance.expirationDate).toISOString().split('T')[0] 
        : '',
      isPrimary: insurance.isPrimary,
      relationshipToSubscriber: 'self', // This would come from extended insurance data
      copayAmount: '',
      deductibleAmount: '',
      notes: '',
    });
    setIsDialogOpen(true);
  };

  const handleSaveInsurance = async (values: InsuranceFormData) => {
    try {
      const insuranceData: Partial<InsuranceInfo> = {
        insuranceCompany: values.insuranceCompany,
        policyNumber: values.policyNumber,
        groupNumber: values.groupNumber || undefined,
        subscriberId: values.subscriberId,
        subscriberName: values.subscriberName,
        effectiveDate: new Date(values.effectiveDate),
        expirationDate: values.expirationDate ? new Date(values.expirationDate) : undefined,
        isPrimary: values.isPrimary,
      };

      let updatedInsurance: InsuranceInfo[];

      if (editingInsurance) {
        // Update existing insurance
        const response = await fetch(`/api/patients/${patient.id}/insurance/${editingInsurance.id}`, {
          method: 'PUT',
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('token')}`,
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(insuranceData),
        });

        if (!response.ok) throw new Error('Failed to update insurance');

        const result = await response.json();
        updatedInsurance = insuranceList.map(ins => 
          ins.id === editingInsurance.id ? result.data : ins
        );
      } else {
        // Add new insurance
        const response = await fetch(`/api/patients/${patient.id}/insurance`, {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('token')}`,
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(insuranceData),
        });

        if (!response.ok) throw new Error('Failed to add insurance');

        const result = await response.json();
        updatedInsurance = [...insuranceList, result.data];
      }

      // Ensure only one primary insurance
      if (values.isPrimary) {
        updatedInsurance = updatedInsurance.map(ins => ({
          ...ins,
          isPrimary: ins.id === (editingInsurance?.id || updatedInsurance[updatedInsurance.length - 1].id),
        }));
      }

      setInsuranceList(updatedInsurance);
      onInsuranceUpdate?.(updatedInsurance);
      setIsDialogOpen(false);
      formik.resetForm();
    } catch (error) {
      console.error('Failed to save insurance:', error);
    }
  };

  const handleDeleteInsurance = async (insuranceId: string) => {
    if (!confirm('Are you sure you want to delete this insurance?')) return;

    try {
      const response = await fetch(`/api/patients/${patient.id}/insurance/${insuranceId}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
        },
      });

      if (!response.ok) throw new Error('Failed to delete insurance');

      const updatedInsurance = insuranceList.filter(ins => ins.id !== insuranceId);
      
      // If deleted insurance was primary, make the first remaining insurance primary
      if (updatedInsurance.length > 0 && !updatedInsurance.some(ins => ins.isPrimary)) {
        updatedInsurance[0].isPrimary = true;
      }

      setInsuranceList(updatedInsurance);
      onInsuranceUpdate?.(updatedInsurance);
    } catch (error) {
      console.error('Failed to delete insurance:', error);
    }
  };

  const handleVerifyInsurance = async (insurance: InsuranceInfo) => {
    setIsVerifying(prev => ({ ...prev, [insurance.id]: true }));

    try {
      const response = await fetch(`/api/insurance/verify`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          patientId: patient.id,
          insuranceId: insurance.id,
          policyNumber: insurance.policyNumber,
          subscriberId: insurance.subscriberId,
          serviceDate: new Date().toISOString(),
        }),
      });

      if (!response.ok) throw new Error('Verification failed');

      const result = await response.json();
      
      setVerificationStatuses(prev => ({
        ...prev,
        [insurance.id]: {
          isVerified: result.data.isActive,
          verificationDate: new Date(),
          status: result.data.isActive ? 'active' : 'inactive',
          eligibilityDetails: result.data.eligibilityDetails,
        },
      }));
    } catch (error) {
      setVerificationStatuses(prev => ({
        ...prev,
        [insurance.id]: {
          isVerified: false,
          verificationDate: new Date(),
          status: 'error',
          errorMessage: error instanceof Error ? error.message : 'Verification failed',
        },
      }));
    } finally {
      setIsVerifying(prev => ({ ...prev, [insurance.id]: false }));
    }
  };

  const loadVerificationStatus = async (insuranceId: string) => {
    try {
      const response = await fetch(`/api/patients/${patient.id}/insurance/${insuranceId}/verification`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
        },
      });

      if (response.ok) {
        const result = await response.json();
        if (result.data) {
          setVerificationStatuses(prev => ({
            ...prev,
            [insuranceId]: result.data,
          }));
        }
      }
    } catch (error) {
      console.error('Failed to load verification status:', error);
    }
  };

  const setPrimaryInsurance = async (insuranceId: string) => {
    try {
      const response = await fetch(`/api/patients/${patient.id}/insurance/${insuranceId}/primary`, {
        method: 'PUT',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
        },
      });

      if (!response.ok) throw new Error('Failed to set primary insurance');

      const updatedInsurance = insuranceList.map(ins => ({
        ...ins,
        isPrimary: ins.id === insuranceId,
      }));

      setInsuranceList(updatedInsurance);
      onInsuranceUpdate?.(updatedInsurance);
    } catch (error) {
      console.error('Failed to set primary insurance:', error);
    }
  };

  const getVerificationIcon = (insurance: InsuranceInfo) => {
    const status = verificationStatuses[insurance.id];
    if (!status) return null;

    switch (status.status) {
      case 'active':
        return <CheckCircleIcon color="success" />;
      case 'inactive':
        return <WarningIcon color="warning" />;
      case 'error':
        return <ErrorIcon color="error" />;
      default:
        return null;
    }
  };

  const getVerificationChip = (insurance: InsuranceInfo) => {
    const status = verificationStatuses[insurance.id];
    if (!status) return null;

    const chipProps = {
      active: { label: 'Verified Active', color: 'success' as const },
      inactive: { label: 'Inactive', color: 'warning' as const },
      error: { label: 'Verification Failed', color: 'error' as const },
      pending: { label: 'Pending', color: 'info' as const },
    };

    const props = chipProps[status.status];
    return <Chip size="small" {...props} />;
  };

  return (
    <>
      <Card>
        <CardContent>
          <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 3 }}>
            <Box sx={{ display: 'flex', alignItems: 'center' }}>
              <InsuranceIcon sx={{ mr: 1, color: 'primary.main' }} />
              <Typography variant="h6" component="h2">
                Insurance Information
              </Typography>
              <Chip label="HIPAA Protected" size="small" color="warning" sx={{ ml: 2 }} />
            </Box>
            
            {!readOnly && (
              <Button
                variant="contained"
                startIcon={<AddIcon />}
                onClick={handleAddInsurance}
              >
                Add Insurance
              </Button>
            )}
          </Box>

          {insuranceList.length === 0 ? (
            <Alert severity="info">
              No insurance information on file. Add insurance to enable prior authorization processing.
            </Alert>
          ) : (
            <List>
              {insuranceList.map((insurance, index) => (
                <React.Fragment key={insurance.id}>
                  <ListItem>
                    <ListItemText
                      primary={
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                          <Typography variant="h6">
                            {insurance.insuranceCompany}
                          </Typography>
                          {insurance.isPrimary && (
                            <Chip label="Primary" color="primary" size="small" />
                          )}
                          {getVerificationChip(insurance)}
                        </Box>
                      }
                      secondary={
                        <Box sx={{ mt: 1 }}>
                          <Grid container spacing={2}>
                            <Grid item xs={12} sm={6}>
                              <Typography variant="body2" color="text.secondary">
                                Policy Number: {insurance.policyNumber}
                              </Typography>
                              <Typography variant="body2" color="text.secondary">
                                Subscriber ID: {insurance.subscriberId}
                              </Typography>
                              <Typography variant="body2" color="text.secondary">
                                Subscriber: {insurance.subscriberName}
                              </Typography>
                            </Grid>
                            <Grid item xs={12} sm={6}>
                              <Typography variant="body2" color="text.secondary">
                                Effective: {formatDate(insurance.effectiveDate)}
                              </Typography>
                              {insurance.expirationDate && (
                                <Typography variant="body2" color="text.secondary">
                                  Expires: {formatDate(insurance.expirationDate)}
                                </Typography>
                              )}
                              {insurance.groupNumber && (
                                <Typography variant="body2" color="text.secondary">
                                  Group: {insurance.groupNumber}
                                </Typography>
                              )}
                            </Grid>
                          </Grid>
                          
                          {verificationStatuses[insurance.id]?.eligibilityDetails && (
                            <Box sx={{ mt: 2, p: 2, bgcolor: 'grey.50', borderRadius: 1 }}>
                              <Typography variant="subtitle2" sx={{ mb: 1 }}>
                                Eligibility Details
                              </Typography>
                              <Grid container spacing={2}>
                                <Grid item xs={6}>
                                  <Typography variant="caption" color="text.secondary">
                                    Coverage Type
                                  </Typography>
                                  <Typography variant="body2">
                                    {verificationStatuses[insurance.id].eligibilityDetails?.coverageType}
                                  </Typography>
                                </Grid>
                                <Grid item xs={6}>
                                  <Typography variant="caption" color="text.secondary">
                                    Copay
                                  </Typography>
                                  <Typography variant="body2">
                                    ${verificationStatuses[insurance.id].eligibilityDetails?.copayAmount || 'N/A'}
                                  </Typography>
                                </Grid>
                              </Grid>
                            </Box>
                          )}
                        </Box>
                      }
                    />
                    
                    <ListItemSecondaryAction>
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                        {getVerificationIcon(insurance)}
                        
                        <Tooltip title="Verify Insurance">
                          <IconButton
                            onClick={() => handleVerifyInsurance(insurance)}
                            disabled={isVerifying[insurance.id]}
                          >
                            {isVerifying[insurance.id] ? (
                              <CircularProgress size={20} />
                            ) : (
                              <RefreshIcon />
                            )}
                          </IconButton>
                        </Tooltip>
                        
                        {!readOnly && (
                          <>
                            <Tooltip title="Edit Insurance">
                              <IconButton onClick={() => handleEditInsurance(insurance)}>
                                <EditIcon />
                              </IconButton>
                            </Tooltip>
                            
                            <Tooltip title="Delete Insurance">
                              <IconButton
                                onClick={() => handleDeleteInsurance(insurance.id)}
                                color="error"
                              >
                                <DeleteIcon />
                              </IconButton>
                            </Tooltip>
                          </>
                        )}
                      </Box>
                    </ListItemSecondaryAction>
                  </ListItem>
                  
                  {index < insuranceList.length - 1 && <Divider />}
                </React.Fragment>
              ))}
            </List>
          )}
        </CardContent>
      </Card>

      {/* Add/Edit Insurance Dialog */}
      <Dialog open={isDialogOpen} onClose={() => setIsDialogOpen(false)} maxWidth="md" fullWidth>
        <DialogTitle>
          {editingInsurance ? 'Edit Insurance' : 'Add Insurance'}
        </DialogTitle>
        <DialogContent>
          <Grid container spacing={3} sx={{ mt: 1 }}>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                name="insuranceCompany"
                label="Insurance Company *"
                value={formik.values.insuranceCompany}
                onChange={formik.handleChange}
                onBlur={formik.handleBlur}
                error={formik.touched.insuranceCompany && Boolean(formik.errors.insuranceCompany)}
                helperText={formik.touched.insuranceCompany && formik.errors.insuranceCompany}
              />
            </Grid>
            
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                name="policyNumber"
                label="Policy Number *"
                value={formik.values.policyNumber}
                onChange={formik.handleChange}
                onBlur={formik.handleBlur}
                error={formik.touched.policyNumber && Boolean(formik.errors.policyNumber)}
                helperText={formik.touched.policyNumber && formik.errors.policyNumber}
              />
            </Grid>
            
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                name="subscriberId"
                label="Subscriber ID *"
                value={formik.values.subscriberId}
                onChange={formik.handleChange}
                onBlur={formik.handleBlur}
                error={formik.touched.subscriberId && Boolean(formik.errors.subscriberId)}
                helperText={formik.touched.subscriberId && formik.errors.subscriberId}
              />
            </Grid>
            
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                name="subscriberName"
                label="Subscriber Name *"
                value={formik.values.subscriberName}
                onChange={formik.handleChange}
                onBlur={formik.handleBlur}
                error={formik.touched.subscriberName && Boolean(formik.errors.subscriberName)}
                helperText={formik.touched.subscriberName && formik.errors.subscriberName}
              />
            </Grid>
            
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                name="effectiveDate"
                label="Effective Date *"
                type="date"
                value={formik.values.effectiveDate}
                onChange={formik.handleChange}
                onBlur={formik.handleBlur}
                error={formik.touched.effectiveDate && Boolean(formik.errors.effectiveDate)}
                helperText={formik.touched.effectiveDate && formik.errors.effectiveDate}
                InputLabelProps={{ shrink: true }}
              />
            </Grid>
            
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                name="expirationDate"
                label="Expiration Date"
                type="date"
                value={formik.values.expirationDate}
                onChange={formik.handleChange}
                onBlur={formik.handleBlur}
                error={formik.touched.expirationDate && Boolean(formik.errors.expirationDate)}
                helperText={formik.touched.expirationDate && formik.errors.expirationDate}
                InputLabelProps={{ shrink: true }}
              />
            </Grid>
            
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                name="groupNumber"
                label="Group Number"
                value={formik.values.groupNumber}
                onChange={formik.handleChange}
                onBlur={formik.handleBlur}
              />
            </Grid>
            
            <Grid item xs={12} sm={6}>
              <FormControlLabel
                control={
                  <Switch
                    name="isPrimary"
                    checked={formik.values.isPrimary}
                    onChange={formik.handleChange}
                  />
                }
                label="Primary Insurance"
              />
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setIsDialogOpen(false)}>Cancel</Button>
          <Button variant="contained" onClick={() => formik.handleSubmit()}>
            {editingInsurance ? 'Update' : 'Add'} Insurance
          </Button>
        </DialogActions>
      </Dialog>
    </>
  );
};

export default InsuranceManagement;
