import React, { useState, useCallback, useEffect } from 'react';
import {
  Box,
  TextField,
  <PERSON>ton,
  Card,
  CardContent,
  Typography,
  List,
  ListItem,
  ListItemText,
  ListItemButton,
  Chip,
  InputAdornment,
  IconButton,
  Divider,
  Alert,
  CircularProgress,
  Grid,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
} from '@mui/material';
import {
  Search as SearchIcon,
  Clear as ClearIcon,
  Person as PersonIcon,
  Badge as BadgeIcon,
  CalendarToday as CalendarIcon,
  Security as SecurityIcon,
} from '@mui/icons-material';
import { Patient, Gender } from '../../types';
import { useAppDispatch, useAppSelector } from '../../store';
import { searchPatients, clearPatientSearch } from '../../store/slices/patientsSlice';

interface PatientSearchProps {
  onPatientSelect?: (patient: Patient) => void;
  showAdvancedSearch?: boolean;
  maxResults?: number;
  placeholder?: string;
  disabled?: boolean;
}

interface SearchCriteria {
  query: string;
  mrn: string;
  firstName: string;
  lastName: string;
  dateOfBirth: string;
  gender: Gender | '';
  phone: string;
  insurancePolicyNumber: string;
}

const PatientSearch: React.FC<PatientSearchProps> = ({
  onPatientSelect,
  showAdvancedSearch = false,
  maxResults = 10,
  placeholder = "Search patients by name, MRN, or phone...",
  disabled = false,
}) => {
  const dispatch = useAppDispatch();
  const { searchResults, isSearching, searchError } = useAppSelector(state => state.patients);
  
  const [searchCriteria, setSearchCriteria] = useState<SearchCriteria>({
    query: '',
    mrn: '',
    firstName: '',
    lastName: '',
    dateOfBirth: '',
    gender: '',
    phone: '',
    insurancePolicyNumber: '',
  });
  
  const [showAdvanced, setShowAdvanced] = useState(showAdvancedSearch);
  const [selectedPatient, setSelectedPatient] = useState<Patient | null>(null);

  // Debounced search function
  const performSearch = useCallback(
    debounce((criteria: SearchCriteria) => {
      if (criteria.query.trim() || criteria.mrn.trim() || criteria.firstName.trim() || 
          criteria.lastName.trim() || criteria.dateOfBirth || criteria.phone.trim() ||
          criteria.insurancePolicyNumber.trim()) {
        dispatch(searchPatients({ 
          ...criteria, 
          limit: maxResults 
        }));
      } else {
        dispatch(clearPatientSearch());
      }
    }, 300),
    [dispatch, maxResults]
  );

  useEffect(() => {
    performSearch(searchCriteria);
  }, [searchCriteria, performSearch]);

  const handleInputChange = (field: keyof SearchCriteria, value: string) => {
    setSearchCriteria(prev => ({
      ...prev,
      [field]: value,
    }));
  };

  const handlePatientSelect = (patient: Patient) => {
    setSelectedPatient(patient);
    onPatientSelect?.(patient);
    
    // Log HIPAA-compliant access
    logPatientAccess(patient.id, 'search_select');
  };

  const handleClearSearch = () => {
    setSearchCriteria({
      query: '',
      mrn: '',
      firstName: '',
      lastName: '',
      dateOfBirth: '',
      gender: '',
      phone: '',
      insurancePolicyNumber: '',
    });
    setSelectedPatient(null);
    dispatch(clearPatientSearch());
  };

  const formatPatientDisplay = (patient: Patient) => {
    const age = calculateAge(patient.dateOfBirth);
    const primaryInsurance = patient.insurance.find(ins => ins.isPrimary);
    
    return {
      primary: `${patient.lastName}, ${patient.firstName}`,
      secondary: `MRN: ${patient.mrn} • ${patient.gender} • Age ${age}`,
      tertiary: primaryInsurance ? `${primaryInsurance.insuranceCompany}` : 'No insurance on file',
    };
  };

  const logPatientAccess = async (patientId: string, action: string) => {
    // HIPAA-compliant audit logging
    try {
      await fetch('/api/audit/patient-access', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
        },
        body: JSON.stringify({
          patientId,
          action,
          timestamp: new Date().toISOString(),
          component: 'PatientSearch',
        }),
      });
    } catch (error) {
      console.error('Failed to log patient access:', error);
    }
  };

  return (
    <Card sx={{ width: '100%', maxWidth: 800 }}>
      <CardContent>
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
          <SecurityIcon sx={{ mr: 1, color: 'primary.main' }} />
          <Typography variant="h6" component="h2">
            Patient Search
          </Typography>
          <Chip 
            label="HIPAA Compliant" 
            size="small" 
            color="success" 
            sx={{ ml: 'auto' }}
          />
        </Box>

        {/* Quick Search */}
        <TextField
          fullWidth
          placeholder={placeholder}
          value={searchCriteria.query}
          onChange={(e) => handleInputChange('query', e.target.value)}
          disabled={disabled}
          InputProps={{
            startAdornment: (
              <InputAdornment position="start">
                <SearchIcon />
              </InputAdornment>
            ),
            endAdornment: (
              <InputAdornment position="end">
                {(searchCriteria.query || isSearching) && (
                  <IconButton
                    onClick={handleClearSearch}
                    disabled={disabled}
                    size="small"
                  >
                    {isSearching ? <CircularProgress size={20} /> : <ClearIcon />}
                  </IconButton>
                )}
              </InputAdornment>
            ),
          }}
          sx={{ mb: 2 }}
        />

        {/* Advanced Search Toggle */}
        <Button
          variant="text"
          onClick={() => setShowAdvanced(!showAdvanced)}
          disabled={disabled}
          sx={{ mb: 2 }}
        >
          {showAdvanced ? 'Hide' : 'Show'} Advanced Search
        </Button>

        {/* Advanced Search Fields */}
        {showAdvanced && (
          <Box sx={{ mb: 2, p: 2, bgcolor: 'grey.50', borderRadius: 1 }}>
            <Typography variant="subtitle2" sx={{ mb: 2 }}>
              Advanced Search Criteria
            </Typography>
            <Grid container spacing={2}>
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label="Medical Record Number"
                  value={searchCriteria.mrn}
                  onChange={(e) => handleInputChange('mrn', e.target.value)}
                  disabled={disabled}
                  InputProps={{
                    startAdornment: (
                      <InputAdornment position="start">
                        <BadgeIcon />
                      </InputAdornment>
                    ),
                  }}
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label="First Name"
                  value={searchCriteria.firstName}
                  onChange={(e) => handleInputChange('firstName', e.target.value)}
                  disabled={disabled}
                  InputProps={{
                    startAdornment: (
                      <InputAdornment position="start">
                        <PersonIcon />
                      </InputAdornment>
                    ),
                  }}
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label="Last Name"
                  value={searchCriteria.lastName}
                  onChange={(e) => handleInputChange('lastName', e.target.value)}
                  disabled={disabled}
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label="Date of Birth"
                  type="date"
                  value={searchCriteria.dateOfBirth}
                  onChange={(e) => handleInputChange('dateOfBirth', e.target.value)}
                  disabled={disabled}
                  InputLabelProps={{ shrink: true }}
                  InputProps={{
                    startAdornment: (
                      <InputAdornment position="start">
                        <CalendarIcon />
                      </InputAdornment>
                    ),
                  }}
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <FormControl fullWidth disabled={disabled}>
                  <InputLabel>Gender</InputLabel>
                  <Select
                    value={searchCriteria.gender}
                    onChange={(e) => handleInputChange('gender', e.target.value)}
                    label="Gender"
                  >
                    <MenuItem value="">All</MenuItem>
                    <MenuItem value={Gender.MALE}>Male</MenuItem>
                    <MenuItem value={Gender.FEMALE}>Female</MenuItem>
                    <MenuItem value={Gender.OTHER}>Other</MenuItem>
                    <MenuItem value={Gender.PREFER_NOT_TO_SAY}>Prefer not to say</MenuItem>
                  </Select>
                </FormControl>
              </Grid>
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label="Phone Number"
                  value={searchCriteria.phone}
                  onChange={(e) => handleInputChange('phone', e.target.value)}
                  disabled={disabled}
                />
              </Grid>
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label="Insurance Policy Number"
                  value={searchCriteria.insurancePolicyNumber}
                  onChange={(e) => handleInputChange('insurancePolicyNumber', e.target.value)}
                  disabled={disabled}
                />
              </Grid>
            </Grid>
          </Box>
        )}

        {/* Search Error */}
        {searchError && (
          <Alert severity="error" sx={{ mb: 2 }}>
            {searchError}
          </Alert>
        )}

        {/* Search Results */}
        {searchResults.length > 0 && (
          <Box>
            <Typography variant="subtitle2" sx={{ mb: 1 }}>
              Search Results ({searchResults.length})
            </Typography>
            <List sx={{ maxHeight: 400, overflow: 'auto' }}>
              {searchResults.map((patient, index) => {
                const display = formatPatientDisplay(patient);
                const isSelected = selectedPatient?.id === patient.id;
                
                return (
                  <React.Fragment key={patient.id}>
                    <ListItem disablePadding>
                      <ListItemButton
                        onClick={() => handlePatientSelect(patient)}
                        selected={isSelected}
                        sx={{
                          '&.Mui-selected': {
                            bgcolor: 'primary.light',
                            '&:hover': {
                              bgcolor: 'primary.main',
                            },
                          },
                        }}
                      >
                        <ListItemText
                          primary={display.primary}
                          secondary={
                            <Box>
                              <Typography variant="body2" color="text.secondary">
                                {display.secondary}
                              </Typography>
                              <Typography variant="caption" color="text.secondary">
                                {display.tertiary}
                              </Typography>
                            </Box>
                          }
                        />
                      </ListItemButton>
                    </ListItem>
                    {index < searchResults.length - 1 && <Divider />}
                  </React.Fragment>
                );
              })}
            </List>
          </Box>
        )}

        {/* No Results */}
        {!isSearching && searchResults.length === 0 && (searchCriteria.query || showAdvanced) && (
          <Box sx={{ textAlign: 'center', py: 3 }}>
            <Typography variant="body2" color="text.secondary">
              No patients found matching your search criteria.
            </Typography>
          </Box>
        )}
      </CardContent>
    </Card>
  );
};

// Utility functions
function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout;
  return (...args: Parameters<T>) => {
    clearTimeout(timeout);
    timeout = setTimeout(() => func(...args), wait);
  };
}

function calculateAge(dateOfBirth: Date): number {
  const today = new Date();
  const birthDate = new Date(dateOfBirth);
  let age = today.getFullYear() - birthDate.getFullYear();
  const monthDiff = today.getMonth() - birthDate.getMonth();
  
  if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
    age--;
  }
  
  return age;
}

export default PatientSearch;
