import React, { useState, useEffect } from 'react';
import {
  Box,
  Card,
  CardContent,
  Ty<PERSON>graphy,
  Button,
  Grid,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Chip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Alert,
  Stepper,
  Step,
  StepLabel,
  StepContent,
  Radio,
  RadioGroup,
  FormControlLabel,
  FormControl,
  FormLabel,
  Checkbox,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Divider,
  CircularProgress,
  Tooltip,
  IconButton,
} from '@mui/material';
import {
  MergeType as MergeIcon,
  Warning as WarningIcon,
  CheckCircle as CheckCircleIcon,
  Person as PersonIcon,
  Security as SecurityIcon,
  Assignment as AssignmentIcon,
  LocalHospital as InsuranceIcon,
  AttachFile as DocumentIcon,
  Timeline as TimelineIcon,
  Visibility as VisibilityIcon,
} from '@mui/icons-material';
import { Patient, PriorAuthorization, Document } from '../../types';
import { formatDate, calculateAge } from '../../utils/formatters';

interface DuplicateMatch {
  patient: Patient;
  confidence: number;
  matchingFields: string[];
  conflictingFields: string[];
  riskLevel: 'low' | 'medium' | 'high';
}

interface MergeConflict {
  field: string;
  primaryValue: any;
  duplicateValue: any;
  selectedValue: any;
  autoResolved: boolean;
}

interface PatientMergeInterfaceProps {
  primaryPatient: Patient;
  onMergeComplete?: (mergedPatient: Patient) => void;
  onCancel?: () => void;
}

const steps = [
  'Find Duplicates',
  'Review Matches',
  'Resolve Conflicts',
  'Confirm Merge',
];

const PatientMergeInterface: React.FC<PatientMergeInterfaceProps> = ({
  primaryPatient,
  onMergeComplete,
  onCancel,
}) => {
  const [activeStep, setActiveStep] = useState(0);
  const [duplicateMatches, setDuplicateMatches] = useState<DuplicateMatch[]>([]);
  const [selectedDuplicates, setSelectedDuplicates] = useState<string[]>([]);
  const [mergeConflicts, setMergeConflicts] = useState<MergeConflict[]>([]);
  const [isSearching, setIsSearching] = useState(false);
  const [isMerging, setIsMerging] = useState(false);
  const [searchError, setSearchError] = useState<string | null>(null);
  const [mergePreview, setMergePreview] = useState<Patient | null>(null);
  const [relatedData, setRelatedData] = useState<{
    priorAuths: PriorAuthorization[];
    documents: Document[];
  }>({ priorAuths: [], documents: [] });

  useEffect(() => {
    findDuplicates();
  }, [primaryPatient]);

  const findDuplicates = async () => {
    setIsSearching(true);
    setSearchError(null);

    try {
      const response = await fetch('/api/patients/find-duplicates', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          patientId: primaryPatient.id,
          searchCriteria: {
            firstName: primaryPatient.firstName,
            lastName: primaryPatient.lastName,
            dateOfBirth: primaryPatient.dateOfBirth,
            phone: primaryPatient.phone,
            email: primaryPatient.email,
          },
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to find duplicates');
      }

      const result = await response.json();
      
      if (result.success) {
        setDuplicateMatches(result.data || []);
      } else {
        throw new Error(result.error || 'Failed to find duplicates');
      }
    } catch (error) {
      setSearchError(error instanceof Error ? error.message : 'Failed to find duplicates');
    } finally {
      setIsSearching(false);
    }
  };

  const handleDuplicateSelection = (patientId: string, selected: boolean) => {
    setSelectedDuplicates(prev => 
      selected 
        ? [...prev, patientId]
        : prev.filter(id => id !== patientId)
    );
  };

  const analyzeConflicts = async () => {
    if (selectedDuplicates.length === 0) return;

    try {
      const response = await fetch('/api/patients/analyze-merge-conflicts', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          primaryPatientId: primaryPatient.id,
          duplicatePatientIds: selectedDuplicates,
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to analyze conflicts');
      }

      const result = await response.json();
      
      if (result.success) {
        setMergeConflicts(result.data.conflicts || []);
        setMergePreview(result.data.preview);
        setRelatedData(result.data.relatedData || { priorAuths: [], documents: [] });
        setActiveStep(2);
      }
    } catch (error) {
      console.error('Failed to analyze conflicts:', error);
    }
  };

  const handleConflictResolution = (conflictIndex: number, selectedValue: any) => {
    setMergeConflicts(prev => prev.map((conflict, index) => 
      index === conflictIndex 
        ? { ...conflict, selectedValue }
        : conflict
    ));
  };

  const executeMerge = async () => {
    setIsMerging(true);

    try {
      const response = await fetch('/api/patients/merge', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          primaryPatientId: primaryPatient.id,
          duplicatePatientIds: selectedDuplicates,
          conflictResolutions: mergeConflicts.reduce((acc, conflict, index) => {
            acc[conflict.field] = conflict.selectedValue;
            return acc;
          }, {} as Record<string, any>),
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to merge patients');
      }

      const result = await response.json();
      
      if (result.success) {
        onMergeComplete?.(result.data);
      } else {
        throw new Error(result.error || 'Merge failed');
      }
    } catch (error) {
      console.error('Merge failed:', error);
    } finally {
      setIsMerging(false);
    }
  };

  const getConfidenceColor = (confidence: number) => {
    if (confidence >= 0.9) return 'success';
    if (confidence >= 0.7) return 'warning';
    return 'error';
  };

  const getRiskColor = (risk: 'low' | 'medium' | 'high') => {
    switch (risk) {
      case 'low': return 'success';
      case 'medium': return 'warning';
      case 'high': return 'error';
      default: return 'default';
    }
  };

  const renderDuplicateFinderStep = () => (
    <Box>
      <Typography variant="h6" sx={{ mb: 2 }}>
        Searching for Duplicate Patients
      </Typography>
      
      {isSearching ? (
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
          <CircularProgress size={24} />
          <Typography>Analyzing patient database for potential duplicates...</Typography>
        </Box>
      ) : searchError ? (
        <Alert severity="error">{searchError}</Alert>
      ) : (
        <Alert severity="success">
          Found {duplicateMatches.length} potential duplicate{duplicateMatches.length !== 1 ? 's' : ''}
        </Alert>
      )}
    </Box>
  );

  const renderMatchReviewStep = () => (
    <Box>
      <Typography variant="h6" sx={{ mb: 2 }}>
        Review Potential Duplicates
      </Typography>
      
      {duplicateMatches.length === 0 ? (
        <Alert severity="info">
          No potential duplicates found. This patient appears to be unique in the system.
        </Alert>
      ) : (
        <TableContainer component={Paper}>
          <Table>
            <TableHead>
              <TableRow>
                <TableCell>Select</TableCell>
                <TableCell>Patient</TableCell>
                <TableCell>Demographics</TableCell>
                <TableCell>Confidence</TableCell>
                <TableCell>Risk Level</TableCell>
                <TableCell>Matching Fields</TableCell>
                <TableCell>Actions</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {duplicateMatches.map((match) => (
                <TableRow key={match.patient.id}>
                  <TableCell>
                    <Checkbox
                      checked={selectedDuplicates.includes(match.patient.id)}
                      onChange={(e) => handleDuplicateSelection(match.patient.id, e.target.checked)}
                    />
                  </TableCell>
                  <TableCell>
                    <Typography variant="subtitle2">
                      {match.patient.lastName}, {match.patient.firstName}
                    </Typography>
                    <Typography variant="caption" color="text.secondary">
                      MRN: {match.patient.mrn}
                    </Typography>
                  </TableCell>
                  <TableCell>
                    <Typography variant="body2">
                      DOB: {formatDate(match.patient.dateOfBirth)}
                    </Typography>
                    <Typography variant="body2">
                      Age: {calculateAge(match.patient.dateOfBirth)}
                    </Typography>
                    <Typography variant="body2">
                      {match.patient.gender}
                    </Typography>
                  </TableCell>
                  <TableCell>
                    <Chip
                      label={`${Math.round(match.confidence * 100)}%`}
                      color={getConfidenceColor(match.confidence)}
                      size="small"
                    />
                  </TableCell>
                  <TableCell>
                    <Chip
                      label={match.riskLevel.toUpperCase()}
                      color={getRiskColor(match.riskLevel)}
                      size="small"
                    />
                  </TableCell>
                  <TableCell>
                    <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                      {match.matchingFields.map((field) => (
                        <Chip
                          key={field}
                          label={field}
                          size="small"
                          variant="outlined"
                          color="success"
                        />
                      ))}
                    </Box>
                  </TableCell>
                  <TableCell>
                    <Tooltip title="View Patient Details">
                      <IconButton size="small">
                        <VisibilityIcon />
                      </IconButton>
                    </Tooltip>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </TableContainer>
      )}
    </Box>
  );

  const renderConflictResolutionStep = () => (
    <Box>
      <Typography variant="h6" sx={{ mb: 2 }}>
        Resolve Data Conflicts
      </Typography>
      
      {mergeConflicts.length === 0 ? (
        <Alert severity="success">
          No conflicts detected. All data can be merged automatically.
        </Alert>
      ) : (
        <Box>
          <Alert severity="warning" sx={{ mb: 2 }}>
            {mergeConflicts.length} conflict{mergeConflicts.length !== 1 ? 's' : ''} detected. 
            Please review and select the correct values.
          </Alert>
          
          {mergeConflicts.map((conflict, index) => (
            <Card key={index} sx={{ mb: 2 }}>
              <CardContent>
                <Typography variant="subtitle1" sx={{ mb: 2 }}>
                  {conflict.field}
                </Typography>
                
                <FormControl component="fieldset">
                  <RadioGroup
                    value={conflict.selectedValue}
                    onChange={(e) => handleConflictResolution(index, e.target.value)}
                  >
                    <FormControlLabel
                      value={conflict.primaryValue}
                      control={<Radio />}
                      label={
                        <Box>
                          <Typography variant="body2">
                            Primary Patient: {String(conflict.primaryValue)}
                          </Typography>
                          <Chip label="Primary" size="small" color="primary" />
                        </Box>
                      }
                    />
                    <FormControlLabel
                      value={conflict.duplicateValue}
                      control={<Radio />}
                      label={
                        <Box>
                          <Typography variant="body2">
                            Duplicate Patient: {String(conflict.duplicateValue)}
                          </Typography>
                          <Chip label="Duplicate" size="small" color="secondary" />
                        </Box>
                      }
                    />
                  </RadioGroup>
                </FormControl>
              </CardContent>
            </Card>
          ))}
        </Box>
      )}
      
      {/* Related Data Summary */}
      <Card sx={{ mt: 3 }}>
        <CardContent>
          <Typography variant="h6" sx={{ mb: 2 }}>
            Related Data to be Merged
          </Typography>
          
          <Grid container spacing={2}>
            <Grid item xs={12} sm={4}>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                <AssignmentIcon color="primary" />
                <Typography variant="body2">
                  {relatedData.priorAuths.length} Prior Authorizations
                </Typography>
              </Box>
            </Grid>
            
            <Grid item xs={12} sm={4}>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                <DocumentIcon color="primary" />
                <Typography variant="body2">
                  {relatedData.documents.length} Documents
                </Typography>
              </Box>
            </Grid>
            
            <Grid item xs={12} sm={4}>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                <InsuranceIcon color="primary" />
                <Typography variant="body2">
                  Insurance Records
                </Typography>
              </Box>
            </Grid>
          </Grid>
        </CardContent>
      </Card>
    </Box>
  );

  const renderConfirmationStep = () => (
    <Box>
      <Typography variant="h6" sx={{ mb: 2 }}>
        Confirm Patient Merge
      </Typography>
      
      <Alert severity="warning" sx={{ mb: 3 }}>
        <Typography variant="subtitle2" sx={{ mb: 1 }}>
          ⚠️ This action cannot be undone
        </Typography>
        <Typography variant="body2">
          The selected duplicate patients will be merged into the primary patient record. 
          All related data (prior authorizations, documents, etc.) will be transferred.
        </Typography>
      </Alert>
      
      {mergePreview && (
        <Card>
          <CardContent>
            <Typography variant="h6" sx={{ mb: 2 }}>
              Merged Patient Preview
            </Typography>
            
            <Grid container spacing={2}>
              <Grid item xs={12} sm={6}>
                <Typography variant="subtitle2">Name</Typography>
                <Typography variant="body1">
                  {mergePreview.lastName}, {mergePreview.firstName}
                </Typography>
              </Grid>
              
              <Grid item xs={12} sm={6}>
                <Typography variant="subtitle2">MRN</Typography>
                <Typography variant="body1">{mergePreview.mrn}</Typography>
              </Grid>
              
              <Grid item xs={12} sm={6}>
                <Typography variant="subtitle2">Date of Birth</Typography>
                <Typography variant="body1">
                  {formatDate(mergePreview.dateOfBirth)}
                </Typography>
              </Grid>
              
              <Grid item xs={12} sm={6}>
                <Typography variant="subtitle2">Phone</Typography>
                <Typography variant="body1">{mergePreview.phone || 'N/A'}</Typography>
              </Grid>
            </Grid>
          </CardContent>
        </Card>
      )}
    </Box>
  );

  return (
    <Card sx={{ maxWidth: 1200, mx: 'auto' }}>
      <CardContent>
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
          <MergeIcon sx={{ mr: 1, color: 'primary.main' }} />
          <Typography variant="h5" component="h1">
            Patient Merge & Deduplication
          </Typography>
          <Chip label="HIPAA Audit Trail" size="small" color="warning" sx={{ ml: 'auto' }} />
        </Box>

        <Stepper activeStep={activeStep} orientation="vertical">
          {steps.map((label, index) => (
            <Step key={label}>
              <StepLabel>{label}</StepLabel>
              <StepContent>
                <Box sx={{ mb: 2 }}>
                  {index === 0 && renderDuplicateFinderStep()}
                  {index === 1 && renderMatchReviewStep()}
                  {index === 2 && renderConflictResolutionStep()}
                  {index === 3 && renderConfirmationStep()}
                </Box>
                
                <Box sx={{ mb: 1 }}>
                  {index === 0 && !isSearching && duplicateMatches.length > 0 && (
                    <Button
                      variant="contained"
                      onClick={() => setActiveStep(1)}
                      sx={{ mt: 1, mr: 1 }}
                    >
                      Review Matches
                    </Button>
                  )}
                  
                  {index === 1 && selectedDuplicates.length > 0 && (
                    <Button
                      variant="contained"
                      onClick={analyzeConflicts}
                      sx={{ mt: 1, mr: 1 }}
                    >
                      Analyze Conflicts
                    </Button>
                  )}
                  
                  {index === 2 && (
                    <Button
                      variant="contained"
                      onClick={() => setActiveStep(3)}
                      sx={{ mt: 1, mr: 1 }}
                    >
                      Review Merge
                    </Button>
                  )}
                  
                  {index === 3 && (
                    <Button
                      variant="contained"
                      color="warning"
                      onClick={executeMerge}
                      disabled={isMerging}
                      sx={{ mt: 1, mr: 1 }}
                    >
                      {isMerging ? 'Merging...' : 'Execute Merge'}
                    </Button>
                  )}
                  
                  <Button
                    onClick={onCancel}
                    sx={{ mt: 1 }}
                  >
                    Cancel
                  </Button>
                </Box>
              </StepContent>
            </Step>
          ))}
        </Stepper>
      </CardContent>
    </Card>
  );
};

export default PatientMergeInterface;
