import React, { useState } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  Grid,
  Chip,
  Avatar,
  IconButton,
  Tooltip,
  Divider,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Alert,
} from '@mui/material';
import {
  Person as PersonIcon,
  Edit as EditIcon,
  Visibility as VisibilityIcon,
  VisibilityOff as VisibilityOffIcon,
  Phone as PhoneIcon,
  Email as EmailIcon,
  Home as HomeIcon,
  Security as SecurityIcon,
  Badge as BadgeIcon,
  CalendarToday as CalendarIcon,
  LocalHospital as InsuranceIcon,
  Warning as WarningIcon,
  CheckCircle as CheckCircleIcon,
} from '@mui/icons-material';
import { Patient, UserRole } from '../../types';
import { useAppSelector } from '../../store';
import { formatPhoneNumber, calculateAge, formatDate } from '../../utils/formatters';

interface PatientDemographicsProps {
  patient: Patient;
  onEdit?: () => void;
  showSensitiveData?: boolean;
  compact?: boolean;
}

const PatientDemographics: React.FC<PatientDemographicsProps> = ({
  patient,
  onEdit,
  showSensitiveData = false,
  compact = false,
}) => {
  const { user } = useAppSelector(state => state.auth);
  const [showPHI, setShowPHI] = useState(showSensitiveData);
  const [showInsuranceDetails, setShowInsuranceDetails] = useState(false);
  const [accessLogDialog, setAccessLogDialog] = useState(false);

  // Role-based access control
  const canViewPHI = user?.role && [
    UserRole.PROVIDER,
    UserRole.STAFF,
    UserRole.BILLING,
    UserRole.PRACTICE_ADMIN,
    UserRole.SYSTEM_ADMIN,
  ].includes(user.role);

  const canEditPatient = user?.role && [
    UserRole.STAFF,
    UserRole.PRACTICE_ADMIN,
    UserRole.SYSTEM_ADMIN,
  ].includes(user.role);

  const age = calculateAge(patient.dateOfBirth);
  const primaryInsurance = patient.insurance.find(ins => ins.isPrimary);

  const handlePHIToggle = async () => {
    if (!showPHI && canViewPHI) {
      // Log PHI access for HIPAA compliance
      await logPHIAccess(patient.id, 'demographics_view');
      setShowPHI(true);
    } else {
      setShowPHI(false);
    }
  };

  const logPHIAccess = async (patientId: string, action: string) => {
    try {
      await fetch('/api/audit/phi-access', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
        },
        body: JSON.stringify({
          patientId,
          action,
          component: 'PatientDemographics',
          timestamp: new Date().toISOString(),
        }),
      });
    } catch (error) {
      console.error('Failed to log PHI access:', error);
    }
  };

  const maskSensitiveData = (data: string, type: 'phone' | 'email' | 'mrn' | 'ssn' = 'phone') => {
    if (showPHI) return data;
    
    switch (type) {
      case 'phone':
        return data.replace(/(\d{3})\d{3}(\d{4})/, '$1-***-$2');
      case 'email':
        const [username, domain] = data.split('@');
        return `${username.slice(0, 2)}***@${domain}`;
      case 'mrn':
        return `***${data.slice(-3)}`;
      case 'ssn':
        return `***-**-${data.slice(-4)}`;
      default:
        return '***';
    }
  };

  const getInitials = (firstName: string, lastName: string) => {
    return `${firstName.charAt(0)}${lastName.charAt(0)}`.toUpperCase();
  };

  if (compact) {
    return (
      <Card sx={{ mb: 2 }}>
        <CardContent sx={{ py: 2 }}>
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <Avatar sx={{ mr: 2, bgcolor: 'primary.main' }}>
              {getInitials(patient.firstName, patient.lastName)}
            </Avatar>
            <Box sx={{ flexGrow: 1 }}>
              <Typography variant="h6">
                {showPHI ? `${patient.lastName}, ${patient.firstName}` : 'Patient ***'}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                MRN: {showPHI ? patient.mrn : maskSensitiveData(patient.mrn, 'mrn')} • 
                {patient.gender} • Age {age}
              </Typography>
            </Box>
            {canViewPHI && (
              <Tooltip title={showPHI ? "Hide PHI" : "Show PHI"}>
                <IconButton onClick={handlePHIToggle}>
                  {showPHI ? <VisibilityOffIcon /> : <VisibilityIcon />}
                </IconButton>
              </Tooltip>
            )}
          </Box>
        </CardContent>
      </Card>
    );
  }

  return (
    <>
      <Card>
        <CardContent>
          {/* Header */}
          <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
            <Avatar 
              sx={{ 
                width: 64, 
                height: 64, 
                mr: 2, 
                bgcolor: 'primary.main',
                fontSize: '1.5rem'
              }}
            >
              {getInitials(patient.firstName, patient.lastName)}
            </Avatar>
            
            <Box sx={{ flexGrow: 1 }}>
              <Typography variant="h4" component="h1">
                {showPHI ? `${patient.lastName}, ${patient.firstName}` : 'Patient Information'}
              </Typography>
              <Box sx={{ display: 'flex', alignItems: 'center', mt: 1 }}>
                <Chip 
                  label={patient.isActive ? 'Active' : 'Inactive'} 
                  color={patient.isActive ? 'success' : 'default'}
                  size="small"
                  sx={{ mr: 1 }}
                />
                <Chip 
                  label="PHI Protected" 
                  color="warning" 
                  size="small"
                  icon={<SecurityIcon />}
                />
              </Box>
            </Box>

            <Box sx={{ display: 'flex', gap: 1 }}>
              {canViewPHI && (
                <Tooltip title={showPHI ? "Hide PHI" : "Show PHI"}>
                  <IconButton onClick={handlePHIToggle} color="primary">
                    {showPHI ? <VisibilityOffIcon /> : <VisibilityIcon />}
                  </IconButton>
                </Tooltip>
              )}
              
              {canEditPatient && onEdit && (
                <Tooltip title="Edit Patient">
                  <IconButton onClick={onEdit} color="primary">
                    <EditIcon />
                  </IconButton>
                </Tooltip>
              )}
            </Box>
          </Box>

          {!canViewPHI && (
            <Alert severity="warning" sx={{ mb: 3 }}>
              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                <WarningIcon sx={{ mr: 1 }} />
                You do not have permission to view Protected Health Information (PHI) for this patient.
              </Box>
            </Alert>
          )}

          {/* Demographics Grid */}
          <Grid container spacing={3}>
            {/* Personal Information */}
            <Grid item xs={12} md={6}>
              <Typography variant="h6" sx={{ mb: 2, display: 'flex', alignItems: 'center' }}>
                <PersonIcon sx={{ mr: 1 }} />
                Personal Information
              </Typography>
              
              <List dense>
                <ListItem>
                  <ListItemIcon>
                    <BadgeIcon />
                  </ListItemIcon>
                  <ListItemText
                    primary="Medical Record Number"
                    secondary={showPHI ? patient.mrn : maskSensitiveData(patient.mrn, 'mrn')}
                  />
                </ListItem>
                
                <ListItem>
                  <ListItemIcon>
                    <CalendarIcon />
                  </ListItemIcon>
                  <ListItemText
                    primary="Date of Birth"
                    secondary={showPHI ? formatDate(patient.dateOfBirth) : '***'}
                  />
                </ListItem>
                
                <ListItem>
                  <ListItemIcon>
                    <PersonIcon />
                  </ListItemIcon>
                  <ListItemText
                    primary="Gender"
                    secondary={patient.gender}
                  />
                </ListItem>
                
                <ListItem>
                  <ListItemIcon>
                    <CalendarIcon />
                  </ListItemIcon>
                  <ListItemText
                    primary="Age"
                    secondary={`${age} years old`}
                  />
                </ListItem>
              </List>
            </Grid>

            {/* Contact Information */}
            <Grid item xs={12} md={6}>
              <Typography variant="h6" sx={{ mb: 2, display: 'flex', alignItems: 'center' }}>
                <PhoneIcon sx={{ mr: 1 }} />
                Contact Information
              </Typography>
              
              <List dense>
                {patient.phone && (
                  <ListItem>
                    <ListItemIcon>
                      <PhoneIcon />
                    </ListItemIcon>
                    <ListItemText
                      primary="Phone"
                      secondary={showPHI ? formatPhoneNumber(patient.phone) : maskSensitiveData(patient.phone, 'phone')}
                    />
                  </ListItem>
                )}
                
                {patient.email && (
                  <ListItem>
                    <ListItemIcon>
                      <EmailIcon />
                    </ListItemIcon>
                    <ListItemText
                      primary="Email"
                      secondary={showPHI ? patient.email : maskSensitiveData(patient.email, 'email')}
                    />
                  </ListItem>
                )}
                
                {patient.address && (
                  <ListItem>
                    <ListItemIcon>
                      <HomeIcon />
                    </ListItemIcon>
                    <ListItemText
                      primary="Address"
                      secondary={
                        showPHI ? (
                          <>
                            {patient.address.street}<br />
                            {patient.address.city}, {patient.address.state} {patient.address.zipCode}
                          </>
                        ) : '***'
                      }
                    />
                  </ListItem>
                )}
              </List>
            </Grid>

            {/* Insurance Information */}
            <Grid item xs={12}>
              <Divider sx={{ my: 2 }} />
              
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 2 }}>
                <Typography variant="h6" sx={{ display: 'flex', alignItems: 'center' }}>
                  <InsuranceIcon sx={{ mr: 1 }} />
                  Insurance Information
                </Typography>
                
                <Button
                  variant="outlined"
                  size="small"
                  onClick={() => setShowInsuranceDetails(true)}
                  disabled={!showPHI}
                >
                  View Details
                </Button>
              </Box>
              
              {primaryInsurance ? (
                <Box sx={{ p: 2, bgcolor: 'grey.50', borderRadius: 1 }}>
                  <Typography variant="subtitle2" color="primary">
                    Primary Insurance
                  </Typography>
                  <Typography variant="body1">
                    {primaryInsurance.insuranceCompany}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Policy: {showPHI ? primaryInsurance.policyNumber : '***'}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Subscriber: {showPHI ? primaryInsurance.subscriberName : '***'}
                  </Typography>
                </Box>
              ) : (
                <Alert severity="info">
                  No insurance information on file
                </Alert>
              )}
            </Grid>

            {/* Audit Information */}
            <Grid item xs={12}>
              <Divider sx={{ my: 2 }} />
              
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                <Typography variant="body2" color="text.secondary">
                  Created: {formatDate(patient.createdAt)} • 
                  Last Updated: {formatDate(patient.updatedAt)}
                </Typography>
                
                <Button
                  variant="text"
                  size="small"
                  onClick={() => setAccessLogDialog(true)}
                  startIcon={<SecurityIcon />}
                >
                  Access Log
                </Button>
              </Box>
            </Grid>
          </Grid>
        </CardContent>
      </Card>

      {/* Insurance Details Dialog */}
      <Dialog
        open={showInsuranceDetails}
        onClose={() => setShowInsuranceDetails(false)}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>Insurance Information</DialogTitle>
        <DialogContent>
          {patient.insurance.map((insurance, index) => (
            <Card key={index} sx={{ mb: 2 }}>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                  <Typography variant="h6">
                    {insurance.insuranceCompany}
                  </Typography>
                  {insurance.isPrimary && (
                    <Chip label="Primary" color="primary" size="small" sx={{ ml: 2 }} />
                  )}
                </Box>
                
                <Grid container spacing={2}>
                  <Grid item xs={6}>
                    <Typography variant="body2" color="text.secondary">
                      Policy Number
                    </Typography>
                    <Typography variant="body1">
                      {insurance.policyNumber}
                    </Typography>
                  </Grid>
                  
                  <Grid item xs={6}>
                    <Typography variant="body2" color="text.secondary">
                      Group Number
                    </Typography>
                    <Typography variant="body1">
                      {insurance.groupNumber || 'N/A'}
                    </Typography>
                  </Grid>
                  
                  <Grid item xs={6}>
                    <Typography variant="body2" color="text.secondary">
                      Subscriber ID
                    </Typography>
                    <Typography variant="body1">
                      {insurance.subscriberId}
                    </Typography>
                  </Grid>
                  
                  <Grid item xs={6}>
                    <Typography variant="body2" color="text.secondary">
                      Subscriber Name
                    </Typography>
                    <Typography variant="body1">
                      {insurance.subscriberName}
                    </Typography>
                  </Grid>
                  
                  <Grid item xs={6}>
                    <Typography variant="body2" color="text.secondary">
                      Effective Date
                    </Typography>
                    <Typography variant="body1">
                      {formatDate(insurance.effectiveDate)}
                    </Typography>
                  </Grid>
                  
                  <Grid item xs={6}>
                    <Typography variant="body2" color="text.secondary">
                      Expiration Date
                    </Typography>
                    <Typography variant="body1">
                      {insurance.expirationDate ? formatDate(insurance.expirationDate) : 'N/A'}
                    </Typography>
                  </Grid>
                </Grid>
              </CardContent>
            </Card>
          ))}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setShowInsuranceDetails(false)}>Close</Button>
        </DialogActions>
      </Dialog>

      {/* Access Log Dialog */}
      <Dialog
        open={accessLogDialog}
        onClose={() => setAccessLogDialog(false)}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>Patient Access Log</DialogTitle>
        <DialogContent>
          <Typography variant="body2" color="text.secondary">
            HIPAA-compliant access logging for patient: {patient.mrn}
          </Typography>
          {/* Access log content would be loaded here */}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setAccessLogDialog(false)}>Close</Button>
        </DialogActions>
      </Dialog>
    </>
  );
};

export default PatientDemographics;
