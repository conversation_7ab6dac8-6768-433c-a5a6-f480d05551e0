import React, { useState, useEffect } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  TextField,
  Button,
  Grid,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  FormControlLabel,
  Checkbox,
  Alert,
  Stepper,
  Step,
  StepLabel,
  StepContent,
  Chip,
  Divider,
  IconButton,
  InputAdornment,
} from '@mui/material';
import {
  Person as PersonIcon,
  ContactPhone as PhoneIcon,
  Email as EmailIcon,
  Home as HomeIcon,
  Security as SecurityIcon,
  Add as AddIcon,
  Remove as RemoveIcon,
  Save as SaveIcon,
  Cancel as CancelIcon,
} from '@mui/icons-material';
import { useFormik } from 'formik';
import * as Yup from 'yup';
import { Patient, Gender, InsuranceInfo, Address } from '../../types';
import { useAppDispatch, useAppSelector } from '../../store';
import { registerPatient, findDuplicatePatients } from '../../store/slices/patientsSlice';

interface PatientRegistrationFormProps {
  onSuccess?: (patient: Patient) => void;
  onCancel?: () => void;
  initialData?: Partial<Patient>;
}

const steps = [
  'Personal Information',
  'Contact Information', 
  'Insurance Information',
  'Review & Submit'
];

const validationSchema = Yup.object({
  firstName: Yup.string()
    .required('First name is required')
    .min(2, 'First name must be at least 2 characters'),
  lastName: Yup.string()
    .required('Last name is required')
    .min(2, 'Last name must be at least 2 characters'),
  dateOfBirth: Yup.date()
    .required('Date of birth is required')
    .max(new Date(), 'Date of birth cannot be in the future'),
  gender: Yup.string()
    .required('Gender is required'),
  mrn: Yup.string()
    .required('Medical Record Number is required')
    .min(3, 'MRN must be at least 3 characters'),
  phone: Yup.string()
    .matches(/^\+?[\d\s\-\(\)]+$/, 'Invalid phone number format'),
  email: Yup.string()
    .email('Invalid email format'),
  address: Yup.object({
    street: Yup.string(),
    city: Yup.string(),
    state: Yup.string(),
    zipCode: Yup.string()
      .matches(/^\d{5}(-\d{4})?$/, 'Invalid ZIP code format'),
    country: Yup.string(),
  }),
  insurance: Yup.array().of(
    Yup.object({
      insuranceCompany: Yup.string().required('Insurance company is required'),
      policyNumber: Yup.string().required('Policy number is required'),
      subscriberId: Yup.string().required('Subscriber ID is required'),
      subscriberName: Yup.string().required('Subscriber name is required'),
      effectiveDate: Yup.date().required('Effective date is required'),
    })
  ),
});

const PatientRegistrationForm: React.FC<PatientRegistrationFormProps> = ({
  onSuccess,
  onCancel,
  initialData,
}) => {
  const dispatch = useAppDispatch();
  const { isRegistering, registrationError, duplicatePatients } = useAppSelector(state => state.patients);
  
  const [activeStep, setActiveStep] = useState(0);
  const [showDuplicateWarning, setShowDuplicateWarning] = useState(false);

  const formik = useFormik({
    initialValues: {
      firstName: initialData?.firstName || '',
      lastName: initialData?.lastName || '',
      dateOfBirth: initialData?.dateOfBirth ? new Date(initialData.dateOfBirth).toISOString().split('T')[0] : '',
      gender: initialData?.gender || '',
      mrn: initialData?.mrn || '',
      phone: initialData?.phone || '',
      email: initialData?.email || '',
      address: {
        street: initialData?.address?.street || '',
        city: initialData?.address?.city || '',
        state: initialData?.address?.state || '',
        zipCode: initialData?.address?.zipCode || '',
        country: initialData?.address?.country || 'US',
      },
      insurance: initialData?.insurance || [{
        id: '',
        isPrimary: true,
        insuranceCompany: '',
        policyNumber: '',
        groupNumber: '',
        subscriberId: '',
        subscriberName: '',
        effectiveDate: new Date().toISOString().split('T')[0],
        expirationDate: '',
      }],
      consentToTreatment: false,
      consentToPortal: false,
      hipaaAcknowledgment: false,
    },
    validationSchema,
    onSubmit: async (values) => {
      try {
        // Check for duplicates before submitting
        await dispatch(findDuplicatePatients({
          firstName: values.firstName,
          lastName: values.lastName,
          dateOfBirth: new Date(values.dateOfBirth),
          phone: values.phone,
        }));

        if (duplicatePatients.length > 0) {
          setShowDuplicateWarning(true);
          return;
        }

        await submitRegistration(values);
      } catch (error) {
        console.error('Registration failed:', error);
      }
    },
  });

  const submitRegistration = async (values: any) => {
    const patientData = {
      ...values,
      dateOfBirth: new Date(values.dateOfBirth),
      practiceId: 'current-practice-id', // This should come from auth context
      isActive: true,
      insurance: values.insurance.map((ins: any, index: number) => ({
        ...ins,
        id: `temp-${index}`,
        effectiveDate: new Date(ins.effectiveDate),
        expirationDate: ins.expirationDate ? new Date(ins.expirationDate) : undefined,
      })),
    };

    const result = await dispatch(registerPatient(patientData));
    
    if (registerPatient.fulfilled.match(result)) {
      onSuccess?.(result.payload);
    }
  };

  const handleNext = () => {
    setActiveStep((prevActiveStep) => prevActiveStep + 1);
  };

  const handleBack = () => {
    setActiveStep((prevActiveStep) => prevActiveStep - 1);
  };

  const addInsurance = () => {
    const newInsurance: InsuranceInfo = {
      id: '',
      isPrimary: formik.values.insurance.length === 0,
      insuranceCompany: '',
      policyNumber: '',
      groupNumber: '',
      subscriberId: '',
      subscriberName: '',
      effectiveDate: new Date(),
      expirationDate: undefined,
    };
    
    formik.setFieldValue('insurance', [...formik.values.insurance, newInsurance]);
  };

  const removeInsurance = (index: number) => {
    const newInsurance = formik.values.insurance.filter((_, i) => i !== index);
    // Ensure at least one insurance is primary
    if (newInsurance.length > 0 && !newInsurance.some(ins => ins.isPrimary)) {
      newInsurance[0].isPrimary = true;
    }
    formik.setFieldValue('insurance', newInsurance);
  };

  const setPrimaryInsurance = (index: number) => {
    const newInsurance = formik.values.insurance.map((ins, i) => ({
      ...ins,
      isPrimary: i === index,
    }));
    formik.setFieldValue('insurance', newInsurance);
  };

  const renderPersonalInformation = () => (
    <Grid container spacing={3}>
      <Grid item xs={12}>
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
          <PersonIcon sx={{ mr: 1, color: 'primary.main' }} />
          <Typography variant="h6">Personal Information</Typography>
          <Chip label="PHI Protected" size="small" color="warning" sx={{ ml: 'auto' }} />
        </Box>
      </Grid>
      
      <Grid item xs={12} sm={6}>
        <TextField
          fullWidth
          name="firstName"
          label="First Name *"
          value={formik.values.firstName}
          onChange={formik.handleChange}
          onBlur={formik.handleBlur}
          error={formik.touched.firstName && Boolean(formik.errors.firstName)}
          helperText={formik.touched.firstName && formik.errors.firstName}
        />
      </Grid>
      
      <Grid item xs={12} sm={6}>
        <TextField
          fullWidth
          name="lastName"
          label="Last Name *"
          value={formik.values.lastName}
          onChange={formik.handleChange}
          onBlur={formik.handleBlur}
          error={formik.touched.lastName && Boolean(formik.errors.lastName)}
          helperText={formik.touched.lastName && formik.errors.lastName}
        />
      </Grid>
      
      <Grid item xs={12} sm={6}>
        <TextField
          fullWidth
          name="dateOfBirth"
          label="Date of Birth *"
          type="date"
          value={formik.values.dateOfBirth}
          onChange={formik.handleChange}
          onBlur={formik.handleBlur}
          error={formik.touched.dateOfBirth && Boolean(formik.errors.dateOfBirth)}
          helperText={formik.touched.dateOfBirth && formik.errors.dateOfBirth}
          InputLabelProps={{ shrink: true }}
        />
      </Grid>
      
      <Grid item xs={12} sm={6}>
        <FormControl fullWidth error={formik.touched.gender && Boolean(formik.errors.gender)}>
          <InputLabel>Gender *</InputLabel>
          <Select
            name="gender"
            value={formik.values.gender}
            onChange={formik.handleChange}
            onBlur={formik.handleBlur}
            label="Gender *"
          >
            <MenuItem value={Gender.MALE}>Male</MenuItem>
            <MenuItem value={Gender.FEMALE}>Female</MenuItem>
            <MenuItem value={Gender.OTHER}>Other</MenuItem>
            <MenuItem value={Gender.PREFER_NOT_TO_SAY}>Prefer not to say</MenuItem>
          </Select>
        </FormControl>
      </Grid>
      
      <Grid item xs={12} sm={6}>
        <TextField
          fullWidth
          name="mrn"
          label="Medical Record Number *"
          value={formik.values.mrn}
          onChange={formik.handleChange}
          onBlur={formik.handleBlur}
          error={formik.touched.mrn && Boolean(formik.errors.mrn)}
          helperText={formik.touched.mrn && formik.errors.mrn}
          InputProps={{
            startAdornment: (
              <InputAdornment position="start">
                <SecurityIcon />
              </InputAdornment>
            ),
          }}
        />
      </Grid>
    </Grid>
  );

  const renderContactInformation = () => (
    <Grid container spacing={3}>
      <Grid item xs={12}>
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
          <PhoneIcon sx={{ mr: 1, color: 'primary.main' }} />
          <Typography variant="h6">Contact Information</Typography>
        </Box>
      </Grid>
      
      <Grid item xs={12} sm={6}>
        <TextField
          fullWidth
          name="phone"
          label="Phone Number"
          value={formik.values.phone}
          onChange={formik.handleChange}
          onBlur={formik.handleBlur}
          error={formik.touched.phone && Boolean(formik.errors.phone)}
          helperText={formik.touched.phone && formik.errors.phone}
          InputProps={{
            startAdornment: (
              <InputAdornment position="start">
                <PhoneIcon />
              </InputAdornment>
            ),
          }}
        />
      </Grid>
      
      <Grid item xs={12} sm={6}>
        <TextField
          fullWidth
          name="email"
          label="Email Address"
          type="email"
          value={formik.values.email}
          onChange={formik.handleChange}
          onBlur={formik.handleBlur}
          error={formik.touched.email && Boolean(formik.errors.email)}
          helperText={formik.touched.email && formik.errors.email}
          InputProps={{
            startAdornment: (
              <InputAdornment position="start">
                <EmailIcon />
              </InputAdornment>
            ),
          }}
        />
      </Grid>
      
      <Grid item xs={12}>
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
          <HomeIcon sx={{ mr: 1, color: 'primary.main' }} />
          <Typography variant="subtitle1">Address</Typography>
        </Box>
      </Grid>
      
      <Grid item xs={12}>
        <TextField
          fullWidth
          name="address.street"
          label="Street Address"
          value={formik.values.address.street}
          onChange={formik.handleChange}
          onBlur={formik.handleBlur}
        />
      </Grid>
      
      <Grid item xs={12} sm={6}>
        <TextField
          fullWidth
          name="address.city"
          label="City"
          value={formik.values.address.city}
          onChange={formik.handleChange}
          onBlur={formik.handleBlur}
        />
      </Grid>
      
      <Grid item xs={12} sm={3}>
        <TextField
          fullWidth
          name="address.state"
          label="State"
          value={formik.values.address.state}
          onChange={formik.handleChange}
          onBlur={formik.handleBlur}
        />
      </Grid>
      
      <Grid item xs={12} sm={3}>
        <TextField
          fullWidth
          name="address.zipCode"
          label="ZIP Code"
          value={formik.values.address.zipCode}
          onChange={formik.handleChange}
          onBlur={formik.handleBlur}
          error={formik.touched.address?.zipCode && Boolean(formik.errors.address?.zipCode)}
          helperText={formik.touched.address?.zipCode && formik.errors.address?.zipCode}
        />
      </Grid>
    </Grid>
  );

  return (
    <Card sx={{ maxWidth: 800, mx: 'auto' }}>
      <CardContent>
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
          <PersonIcon sx={{ mr: 1, color: 'primary.main' }} />
          <Typography variant="h5" component="h1">
            Patient Registration
          </Typography>
          <Chip label="HIPAA Compliant" size="small" color="success" sx={{ ml: 'auto' }} />
        </Box>

        {registrationError && (
          <Alert severity="error" sx={{ mb: 3 }}>
            {registrationError}
          </Alert>
        )}

        {showDuplicateWarning && duplicatePatients.length > 0 && (
          <Alert 
            severity="warning" 
            sx={{ mb: 3 }}
            action={
              <Button 
                color="inherit" 
                size="small" 
                onClick={() => setShowDuplicateWarning(false)}
              >
                Continue Anyway
              </Button>
            }
          >
            Potential duplicate patients found. Please review before continuing.
          </Alert>
        )}

        <Stepper activeStep={activeStep} orientation="vertical">
          {steps.map((label, index) => (
            <Step key={label}>
              <StepLabel>{label}</StepLabel>
              <StepContent>
                <Box sx={{ mb: 2 }}>
                  {index === 0 && renderPersonalInformation()}
                  {index === 1 && renderContactInformation()}
                  {/* Additional steps would be implemented here */}
                </Box>
                
                <Box sx={{ mb: 1 }}>
                  <Button
                    variant="contained"
                    onClick={index === steps.length - 1 ? formik.handleSubmit : handleNext}
                    sx={{ mt: 1, mr: 1 }}
                    disabled={isRegistering}
                    startIcon={index === steps.length - 1 ? <SaveIcon /> : undefined}
                  >
                    {index === steps.length - 1 ? 'Register Patient' : 'Continue'}
                  </Button>
                  
                  <Button
                    disabled={index === 0}
                    onClick={handleBack}
                    sx={{ mt: 1, mr: 1 }}
                  >
                    Back
                  </Button>
                  
                  <Button
                    onClick={onCancel}
                    sx={{ mt: 1 }}
                    startIcon={<CancelIcon />}
                  >
                    Cancel
                  </Button>
                </Box>
              </StepContent>
            </Step>
          ))}
        </Stepper>
      </CardContent>
    </Card>
  );
};

export default PatientRegistrationForm;
