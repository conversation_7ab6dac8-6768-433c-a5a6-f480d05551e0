// OCTAVE Healthcare Platform - Accessibility Control Panel
// Healthcare-specific accessibility settings and controls

import React, { useState, useEffect } from 'react';
import {
  Box,
  Card,
  CardContent,
  CardHeader,
  Switch,
  FormControlLabel,
  Button,
  Typography,
  Divider,
  Alert,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  ListItemSecondaryAction,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  useTheme,
} from '@mui/material';
import {
  AccessibilityRounded,
  ContrastRounded,
  TextFieldsRounded,
  MotionPhotosOffRounded,
  VolumeUpRounded,
  KeyboardRounded,
  ColorLensRounded,
  HelpRounded,
  SettingsRounded,
} from '@mui/icons-material';

import { 
  HealthcareAccessibilityUtils, 
  AccessibilityConfig,
  HealthcareKeyboardShortcuts 
} from '@/utils/healthcareAccessibility';

interface AccessibilityPanelProps {
  onConfigChange?: (config: AccessibilityConfig) => void;
}

export const AccessibilityPanel: React.FC<AccessibilityPanelProps> = ({
  onConfigChange,
}) => {
  const theme = useTheme();
  const [config, setConfig] = useState<AccessibilityConfig>(
    HealthcareAccessibilityUtils.getConfig()
  );
  const [showShortcuts, setShowShortcuts] = useState(false);

  useEffect(() => {
    // Save config to localStorage when it changes
    localStorage.setItem('healthcare-accessibility-config', JSON.stringify(config));
    onConfigChange?.(config);
  }, [config, onConfigChange]);

  const handleConfigChange = (key: keyof AccessibilityConfig) => (
    event: React.ChangeEvent<HTMLInputElement>
  ) => {
    const newConfig = { ...config, [key]: event.target.checked };
    setConfig(newConfig);
    HealthcareAccessibilityUtils.setConfig(newConfig);
    
    // Announce change to screen readers
    HealthcareAccessibilityUtils.announceToScreenReader(
      `${key.replace(/([A-Z])/g, ' $1').toLowerCase()} ${event.target.checked ? 'enabled' : 'disabled'}`
    );
  };

  const resetToDefaults = () => {
    const defaultConfig: AccessibilityConfig = {
      highContrast: false,
      largeText: false,
      reducedMotion: false,
      screenReaderOptimized: false,
      keyboardNavigation: true,
      voiceNavigation: false,
      colorBlindFriendly: false,
    };
    
    setConfig(defaultConfig);
    HealthcareAccessibilityUtils.setConfig(defaultConfig);
    HealthcareAccessibilityUtils.announceToScreenReader('Accessibility settings reset to defaults');
  };

  const accessibilityOptions = [
    {
      key: 'highContrast' as keyof AccessibilityConfig,
      label: 'High Contrast Mode',
      description: 'Increases contrast for better visibility in clinical environments',
      icon: <ContrastRounded />,
    },
    {
      key: 'largeText' as keyof AccessibilityConfig,
      label: 'Large Text',
      description: 'Increases text size for improved readability',
      icon: <TextFieldsRounded />,
    },
    {
      key: 'reducedMotion' as keyof AccessibilityConfig,
      label: 'Reduced Motion',
      description: 'Minimizes animations and transitions',
      icon: <MotionPhotosOffRounded />,
    },
    {
      key: 'screenReaderOptimized' as keyof AccessibilityConfig,
      label: 'Screen Reader Optimization',
      description: 'Enhanced support for screen readers with medical terminology',
      icon: <VolumeUpRounded />,
    },
    {
      key: 'keyboardNavigation' as keyof AccessibilityConfig,
      label: 'Enhanced Keyboard Navigation',
      description: 'Healthcare-specific keyboard shortcuts and navigation',
      icon: <KeyboardRounded />,
    },
    {
      key: 'colorBlindFriendly' as keyof AccessibilityConfig,
      label: 'Color Blind Friendly',
      description: 'Optimized color scheme for color vision deficiencies',
      icon: <ColorLensRounded />,
    },
  ];

  return (
    <>
      <Card>
        <CardHeader
          title="Accessibility Settings"
          subheader="Healthcare-specific accessibility options for better usability"
          avatar={<AccessibilityRounded />}
          action={
            <IconButton onClick={() => setShowShortcuts(true)}>
              <HelpRounded />
            </IconButton>
          }
        />
        <CardContent>
          <Alert severity="info" sx={{ mb: 2 }}>
            <Typography variant="body2">
              These settings are designed to meet healthcare-specific accessibility needs 
              and comply with WCAG 2.1 AA standards.
            </Typography>
          </Alert>

          <List>
            {accessibilityOptions.map((option, index) => (
              <React.Fragment key={option.key}>
                <ListItem>
                  <ListItemIcon>
                    {option.icon}
                  </ListItemIcon>
                  <ListItemText
                    primary={option.label}
                    secondary={option.description}
                  />
                  <ListItemSecondaryAction>
                    <FormControlLabel
                      control={
                        <Switch
                          checked={config[option.key]}
                          onChange={handleConfigChange(option.key)}
                          color="primary"
                        />
                      }
                      label=""
                    />
                  </ListItemSecondaryAction>
                </ListItem>
                {index < accessibilityOptions.length - 1 && <Divider />}
              </React.Fragment>
            ))}
          </List>

          <Box sx={{ mt: 3, display: 'flex', gap: 2 }}>
            <Button
              variant="outlined"
              onClick={resetToDefaults}
              startIcon={<SettingsRounded />}
            >
              Reset to Defaults
            </Button>
            <Button
              variant="outlined"
              onClick={() => setShowShortcuts(true)}
              startIcon={<KeyboardRounded />}
            >
              View Shortcuts
            </Button>
          </Box>

          {/* Current Status */}
          <Box sx={{ mt: 3, p: 2, bgcolor: 'grey.50', borderRadius: 1 }}>
            <Typography variant="subtitle2" gutterBottom>
              Current Accessibility Status
            </Typography>
            <Typography variant="body2" color="text.secondary">
              {Object.entries(config).filter(([, value]) => value).length} of {Object.keys(config).length} features enabled
            </Typography>
          </Box>
        </CardContent>
      </Card>

      {/* Keyboard Shortcuts Dialog */}
      <Dialog
        open={showShortcuts}
        onClose={() => setShowShortcuts(false)}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <KeyboardRounded />
            <Typography variant="h6">Healthcare Keyboard Shortcuts</Typography>
          </Box>
        </DialogTitle>
        <DialogContent>
          <Typography variant="body2" color="text.secondary" paragraph>
            These keyboard shortcuts are designed for efficient navigation in healthcare workflows.
          </Typography>

          <Box sx={{ display: 'grid', gap: 3 }}>
            {/* Navigation Shortcuts */}
            <Box>
              <Typography variant="subtitle1" gutterBottom fontWeight="bold">
                Navigation
              </Typography>
              <List dense>
                <ListItem>
                  <ListItemText
                    primary="Alt + P"
                    secondary="Go to patients list"
                  />
                </ListItem>
                <ListItem>
                  <ListItemText
                    primary="Alt + A"
                    secondary="Go to prior authorizations"
                  />
                </ListItem>
                <ListItem>
                  <ListItemText
                    primary="Alt + M"
                    secondary="Go to messages"
                  />
                </ListItem>
                <ListItem>
                  <ListItemText
                    primary="Alt + D"
                    secondary="Go to dashboard"
                  />
                </ListItem>
              </List>
            </Box>

            {/* Action Shortcuts */}
            <Box>
              <Typography variant="subtitle1" gutterBottom fontWeight="bold">
                Quick Actions
              </Typography>
              <List dense>
                <ListItem>
                  <ListItemText
                    primary="Ctrl + N"
                    secondary="Create new patient"
                  />
                </ListItem>
                <ListItem>
                  <ListItemText
                    primary="Ctrl + S"
                    secondary="Save current form"
                  />
                </ListItem>
                <ListItem>
                  <ListItemText
                    primary="Ctrl + F"
                    secondary="Search patients"
                  />
                </ListItem>
                <ListItem>
                  <ListItemText
                    primary="Escape"
                    secondary="Cancel current action"
                  />
                </ListItem>
              </List>
            </Box>

            {/* Emergency Shortcuts */}
            <Box>
              <Typography variant="subtitle1" gutterBottom fontWeight="bold">
                Emergency
              </Typography>
              <List dense>
                <ListItem>
                  <ListItemText
                    primary="F1"
                    secondary="Emergency help"
                  />
                </ListItem>
                <ListItem>
                  <ListItemText
                    primary="F2"
                    secondary="Emergency contact"
                  />
                </ListItem>
              </List>
            </Box>

            {/* Accessibility Shortcuts */}
            <Box>
              <Typography variant="subtitle1" gutterBottom fontWeight="bold">
                Accessibility
              </Typography>
              <List dense>
                <ListItem>
                  <ListItemText
                    primary="Alt + H"
                    secondary="Toggle high contrast"
                  />
                </ListItem>
                <ListItem>
                  <ListItemText
                    primary="Alt + T"
                    secondary="Toggle large text"
                  />
                </ListItem>
                <ListItem>
                  <ListItemText
                    primary="Alt + R"
                    secondary="Toggle reduced motion"
                  />
                </ListItem>
              </List>
            </Box>
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setShowShortcuts(false)}>Close</Button>
        </DialogActions>
      </Dialog>
    </>
  );
};

export default AccessibilityPanel;
