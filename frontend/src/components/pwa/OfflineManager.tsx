// OCTAVE Healthcare Platform - Offline Manager Component
// PWA offline functionality for healthcare workflows

import React, { useState, useEffect } from 'react';
import {
  Box,
  Alert,
  Button,
  Typography,
  Snackbar,
  IconButton,
  Badge,
  Tooltip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  useTheme,
} from '@mui/material';
import {
  CloudOffRounded,
  CloudRounded,
  SyncRounded,
  OfflineBoltRounded,
  CloseRounded,
  StorageRounded,
  ScheduleRounded,
} from '@mui/icons-material';

interface OfflineRequest {
  id: string;
  url: string;
  method: string;
  timestamp: number;
  description: string;
}

interface OfflineManagerProps {
  onOfflineStatusChange?: (isOffline: boolean) => void;
}

export const OfflineManager: React.FC<OfflineManagerProps> = ({
  onOfflineStatusChange,
}) => {
  const theme = useTheme();
  const [isOffline, setIsOffline] = useState(!navigator.onLine);
  const [showOfflineAlert, setShowOfflineAlert] = useState(false);
  const [pendingRequests, setPendingRequests] = useState<OfflineRequest[]>([]);
  const [showPendingDialog, setShowPendingDialog] = useState(false);
  const [syncInProgress, setSyncInProgress] = useState(false);

  useEffect(() => {
    const handleOnline = () => {
      setIsOffline(false);
      setShowOfflineAlert(false);
      onOfflineStatusChange?.(false);
      
      // Trigger background sync when coming back online
      if ('serviceWorker' in navigator && 'sync' in window.ServiceWorkerRegistration.prototype) {
        navigator.serviceWorker.ready.then((registration) => {
          return registration.sync.register('offline-requests-sync');
        });
      }
    };

    const handleOffline = () => {
      setIsOffline(true);
      setShowOfflineAlert(true);
      onOfflineStatusChange?.(true);
    };

    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    // Load pending requests from IndexedDB
    loadPendingRequests();

    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, [onOfflineStatusChange]);

  const loadPendingRequests = async () => {
    try {
      // This would load from IndexedDB in a real implementation
      // For now, we'll simulate some pending requests
      const mockRequests: OfflineRequest[] = [
        {
          id: '1',
          url: '/api/patients',
          method: 'POST',
          timestamp: Date.now() - 300000, // 5 minutes ago
          description: 'New patient registration',
        },
        {
          id: '2',
          url: '/api/prior-authorizations',
          method: 'PUT',
          timestamp: Date.now() - 600000, // 10 minutes ago
          description: 'Prior authorization update',
        },
      ];
      
      if (isOffline) {
        setPendingRequests(mockRequests);
      }
    } catch (error) {
      console.error('Failed to load pending requests:', error);
    }
  };

  const handleManualSync = async () => {
    setSyncInProgress(true);
    
    try {
      // Trigger manual sync
      if ('serviceWorker' in navigator) {
        const registration = await navigator.serviceWorker.ready;
        if ('sync' in registration) {
          await registration.sync.register('offline-requests-sync');
        }
      }
      
      // Simulate sync delay
      setTimeout(() => {
        setPendingRequests([]);
        setSyncInProgress(false);
      }, 2000);
    } catch (error) {
      console.error('Manual sync failed:', error);
      setSyncInProgress(false);
    }
  };

  const formatTimestamp = (timestamp: number): string => {
    const now = Date.now();
    const diff = now - timestamp;
    const minutes = Math.floor(diff / 60000);
    const hours = Math.floor(minutes / 60);
    
    if (hours > 0) return `${hours}h ago`;
    if (minutes > 0) return `${minutes}m ago`;
    return 'Just now';
  };

  return (
    <>
      {/* Offline Status Indicator */}
      <Box sx={{ position: 'fixed', top: 16, right: 16, zIndex: theme.zIndex.appBar + 1 }}>
        <Tooltip title={isOffline ? 'Offline Mode' : 'Online'}>
          <IconButton
            onClick={() => setShowPendingDialog(true)}
            sx={{
              backgroundColor: isOffline ? 'warning.main' : 'success.main',
              color: 'white',
              '&:hover': {
                backgroundColor: isOffline ? 'warning.dark' : 'success.dark',
              },
            }}
          >
            <Badge badgeContent={pendingRequests.length} color="error">
              {isOffline ? <CloudOffRounded /> : <CloudRounded />}
            </Badge>
          </IconButton>
        </Tooltip>
      </Box>

      {/* Offline Alert Snackbar */}
      <Snackbar
        open={showOfflineAlert}
        onClose={() => setShowOfflineAlert(false)}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
      >
        <Alert
          severity="warning"
          action={
            <IconButton size="small" onClick={() => setShowOfflineAlert(false)}>
              <CloseRounded />
            </IconButton>
          }
        >
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <OfflineBoltRounded />
            <Typography variant="body2">
              You're offline. Changes will sync when connection is restored.
            </Typography>
          </Box>
        </Alert>
      </Snackbar>

      {/* Pending Requests Dialog */}
      <Dialog
        open={showPendingDialog}
        onClose={() => setShowPendingDialog(false)}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <StorageRounded />
            <Typography variant="h6">Offline Data</Typography>
            {isOffline && (
              <Badge badgeContent={pendingRequests.length} color="warning">
                <CloudOffRounded />
              </Badge>
            )}
          </Box>
        </DialogTitle>
        <DialogContent>
          {isOffline ? (
            <Alert severity="info" sx={{ mb: 2 }}>
              <Typography variant="body2">
                You're currently offline. The following changes are stored locally 
                and will sync automatically when you're back online.
              </Typography>
            </Alert>
          ) : (
            <Alert severity="success" sx={{ mb: 2 }}>
              <Typography variant="body2">
                You're online. All data is synchronized with the server.
              </Typography>
            </Alert>
          )}

          {pendingRequests.length > 0 ? (
            <List>
              {pendingRequests.map((request) => (
                <ListItem key={request.id}>
                  <ListItemIcon>
                    <ScheduleRounded color="warning" />
                  </ListItemIcon>
                  <ListItemText
                    primary={request.description}
                    secondary={
                      <Box>
                        <Typography variant="caption" display="block">
                          {request.method} {request.url}
                        </Typography>
                        <Typography variant="caption" color="text.secondary">
                          Created: {formatTimestamp(request.timestamp)}
                        </Typography>
                      </Box>
                    }
                  />
                </ListItem>
              ))}
            </List>
          ) : (
            <Typography variant="body2" color="text.secondary" sx={{ textAlign: 'center', py: 2 }}>
              No pending offline changes
            </Typography>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setShowPendingDialog(false)}>
            Close
          </Button>
          {!isOffline && pendingRequests.length > 0 && (
            <Button
              onClick={handleManualSync}
              disabled={syncInProgress}
              startIcon={syncInProgress ? <SyncRounded className="spin" /> : <SyncRounded />}
              variant="contained"
            >
              {syncInProgress ? 'Syncing...' : 'Sync Now'}
            </Button>
          )}
        </DialogActions>
      </Dialog>

      {/* CSS for spinning sync icon */}
      <style>
        {`
          @keyframes spin {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
          }
          .spin {
            animation: spin 1s linear infinite;
          }
        `}
      </style>
    </>
  );
};

// Hook for offline functionality
export const useOfflineCapability = () => {
  const [isOffline, setIsOffline] = useState(!navigator.onLine);
  const [hasOfflineData, setHasOfflineData] = useState(false);

  useEffect(() => {
    const handleOnline = () => setIsOffline(false);
    const handleOffline = () => setIsOffline(true);

    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, []);

  const storeOfflineData = async (key: string, data: any) => {
    try {
      // Store in IndexedDB for offline access
      localStorage.setItem(`offline_${key}`, JSON.stringify({
        data,
        timestamp: Date.now(),
      }));
      setHasOfflineData(true);
    } catch (error) {
      console.error('Failed to store offline data:', error);
    }
  };

  const getOfflineData = async (key: string) => {
    try {
      const stored = localStorage.getItem(`offline_${key}`);
      if (stored) {
        const { data, timestamp } = JSON.parse(stored);
        // Check if data is not too old (24 hours)
        if (Date.now() - timestamp < 24 * 60 * 60 * 1000) {
          return data;
        }
      }
    } catch (error) {
      console.error('Failed to get offline data:', error);
    }
    return null;
  };

  const clearOfflineData = async (key?: string) => {
    try {
      if (key) {
        localStorage.removeItem(`offline_${key}`);
      } else {
        // Clear all offline data
        Object.keys(localStorage)
          .filter(k => k.startsWith('offline_'))
          .forEach(k => localStorage.removeItem(k));
      }
      setHasOfflineData(false);
    } catch (error) {
      console.error('Failed to clear offline data:', error);
    }
  };

  return {
    isOffline,
    hasOfflineData,
    storeOfflineData,
    getOfflineData,
    clearOfflineData,
  };
};

export default OfflineManager;
