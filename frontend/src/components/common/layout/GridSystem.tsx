// OCTAVE Healthcare Platform - Standardized Grid System
// MUI Grid-based responsive layout utilities with healthcare-specific patterns

import React from 'react';
import { Grid, GridProps, Box, BoxProps } from '@mui/material';

// Standardized responsive grid using MUI Grid
interface ResponsiveGridProps {
  children: React.ReactNode;
  spacing?: number;
  columns?: {
    xs?: number;
    sm?: number;
    md?: number;
    lg?: number;
    xl?: number;
  };
  alignItems?: 'flex-start' | 'center' | 'flex-end' | 'stretch';
  justifyContent?: 'flex-start' | 'center' | 'flex-end' | 'space-between' | 'space-around' | 'space-evenly';
  direction?: 'row' | 'column';
  wrap?: 'nowrap' | 'wrap' | 'wrap-reverse';
  sx?: BoxProps['sx'];
}

export const ResponsiveGrid: React.FC<ResponsiveGridProps> = ({
  children,
  spacing = 2,
  columns = { xs: 1, sm: 2, md: 3, lg: 4 },
  alignItems = 'stretch',
  justifyContent = 'flex-start',
  direction = 'row',
  wrap = 'wrap',
  sx,
}) => {
  // Convert children to array for processing
  const childArray = React.Children.toArray(children);

  return (
    <Grid
      container
      spacing={spacing}
      direction={direction}
      wrap={wrap}
      alignItems={alignItems}
      justifyContent={justifyContent}
      sx={{
        // Ensure grid items maintain minimum sizes for touch targets
        '& > .MuiGrid-item': {
          minHeight: 44,
        },
        ...sx,
      }}
    >
      {childArray.map((child, index) => (
        <Grid
          item
          key={index}
          xs={12 / (columns.xs || 1)}
          sm={12 / (columns.sm || 2)}
          md={12 / (columns.md || 3)}
          lg={12 / (columns.lg || 4)}
          xl={12 / (columns.xl || 4)}
        >
          {child}
        </Grid>
      ))}
    </Grid>
  );
};

// Enhanced MUI Grid wrapper with healthcare-specific defaults
interface HealthcareGridProps extends Omit<GridProps, 'container' | 'item'> {
  variant?: 'dashboard' | 'form' | 'list' | 'card-grid';
  children: React.ReactNode;
}

export const HealthcareGrid: React.FC<HealthcareGridProps> = ({
  variant = 'dashboard',
  children,
  spacing,
  sx,
  ...props
}) => {
  // Healthcare-specific spacing defaults
  const getDefaultSpacing = () => {
    switch (variant) {
      case 'dashboard':
        return 3;
      case 'form':
        return 2;
      case 'list':
        return 1;
      case 'card-grid':
        return 2;
      default:
        return 2;
    }
  };

  // Healthcare-specific styling
  const getVariantStyles = () => {
    switch (variant) {
      case 'dashboard':
        return {
          '& .MuiGrid-item': {
            minHeight: 120,
          },
        };
      case 'form':
        return {
          '& .MuiGrid-item': {
            minHeight: 56,
          },
        };
      case 'list':
        return {
          '& .MuiGrid-item': {
            minHeight: 48,
          },
        };
      case 'card-grid':
        return {
          '& .MuiGrid-item': {
            minHeight: 200,
            display: 'flex',
            flexDirection: 'column',
          },
        };
      default:
        return {};
    }
  };

  return (
    <Grid
      container
      spacing={spacing || getDefaultSpacing()}
      sx={{
        ...getVariantStyles(),
        ...sx,
      }}
      {...props}
    >
      {children}
    </Grid>
  );
};

// Grid item wrapper with healthcare-specific patterns
interface HealthcareGridItemProps extends GridProps {
  variant?: 'stat-card' | 'action-card' | 'form-field' | 'list-item';
  children: React.ReactNode;
}

export const HealthcareGridItem: React.FC<HealthcareGridItemProps> = ({
  variant = 'stat-card',
  children,
  sx,
  ...props
}) => {
  const getVariantStyles = () => {
    switch (variant) {
      case 'stat-card':
        return {
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          justifyContent: 'center',
          textAlign: 'center',
          minHeight: 120,
        };
      case 'action-card':
        return {
          display: 'flex',
          flexDirection: 'column',
          minHeight: 100,
        };
      case 'form-field':
        return {
          display: 'flex',
          alignItems: 'center',
          minHeight: 56,
        };
      case 'list-item':
        return {
          display: 'flex',
          alignItems: 'center',
          minHeight: 48,
        };
      default:
        return {};
    }
  };

  return (
    <Grid
      item
      sx={{
        ...getVariantStyles(),
        ...sx,
      }}
      {...props}
    >
      {children}
    </Grid>
  );
};

// Responsive breakpoint utilities
export const breakpoints = {
  xs: 0,
  sm: 600,
  md: 900,
  lg: 1200,
  xl: 1536,
} as const;

// Helper function to create responsive values
export const createResponsiveValue = <T,>(values: {
  xs?: T;
  sm?: T;
  md?: T;
  lg?: T;
  xl?: T;
}) => values;

// Common grid patterns for healthcare applications
export const GridPatterns = {
  // Dashboard layout: 4 columns on large screens, responsive down
  dashboard: createResponsiveValue({
    xs: 1,
    sm: 2,
    md: 3,
    lg: 4,
    xl: 4,
  }),

  // Form layout: 2 columns on medium+ screens
  form: createResponsiveValue({
    xs: 1,
    sm: 1,
    md: 2,
    lg: 2,
    xl: 2,
  }),

  // Card grid: responsive card layout
  cardGrid: createResponsiveValue({
    xs: 1,
    sm: 2,
    md: 3,
    lg: 4,
    xl: 5,
  }),

  // Stats layout: 2-4 columns based on screen size
  stats: createResponsiveValue({
    xs: 2,
    sm: 2,
    md: 4,
    lg: 4,
    xl: 4,
  }),

  // List layout: single column with responsive spacing
  list: createResponsiveValue({
    xs: 1,
    sm: 1,
    md: 1,
    lg: 1,
    xl: 1,
  }),
};

// Grid spacing utilities
export const GridSpacing = {
  tight: 1,
  normal: 2,
  comfortable: 3,
  spacious: 4,
} as const;

// Export legacy ResponsiveGrid for backward compatibility (deprecated)
export { ResponsiveGrid as LegacyResponsiveGrid };

export default {
  ResponsiveGrid,
  HealthcareGrid,
  HealthcareGridItem,
  GridPatterns,
  GridSpacing,
  breakpoints,
  createResponsiveValue,
};
