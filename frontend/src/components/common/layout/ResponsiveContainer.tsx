// OCTAVE Healthcare Platform - Responsive Container Component
// WCAG 2.1 AA Compliant Responsive Layout System

import React from 'react';
import { Box, Container, useTheme, useMediaQuery } from '@mui/material';
import { styled } from '@mui/material/styles';

interface ResponsiveContainerProps {
  children: React.ReactNode;
  maxWidth?: 'xs' | 'sm' | 'md' | 'lg' | 'xl' | false;
  padding?: boolean;
  fullHeight?: boolean;
  centerContent?: boolean;
  className?: string;
}

const StyledContainer = styled(Container, {
  shouldForwardProp: (prop) =>
    !['fullHeight', 'centerContent'].includes(prop as string),
})<{
  fullHeight?: boolean;
  centerContent?: boolean;
}>(({ fullHeight, centerContent }) => ({
  ...(fullHeight && {
    minHeight: '100vh',
    display: 'flex',
    flexDirection: 'column',
  }),
  ...(centerContent && {
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
  }),
  // Ensure minimum touch target sizes for accessibility
  '& button, & [role="button"]': {
    minHeight: 44,
    minWidth: 44,
  },
  // High contrast mode support
  '@media (prefers-contrast: high)': {
    '& *': {
      borderColor: 'currentColor !important',
    },
  },
  // Reduced motion support
  '@media (prefers-reduced-motion: reduce)': {
    '& *': {
      animationDuration: '0.01ms !important',
      animationIterationCount: '1 !important',
      transitionDuration: '0.01ms !important',
    },
  },
}));

const ResponsiveContainer: React.FC<ResponsiveContainerProps> = ({
  children,
  maxWidth = 'lg',
  padding = true,
  fullHeight = false,
  centerContent = false,
  className,
}) => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  const isTablet = useMediaQuery(theme.breakpoints.between('sm', 'md'));

  return (
    <StyledContainer
      maxWidth={maxWidth}
      fullHeight={fullHeight}
      centerContent={centerContent}
      {...(className && { className })}
      sx={{
        px: padding ? (isMobile ? 2 : isTablet ? 3 : 4) : 0,
        py: padding ? (isMobile ? 2 : 3) : 0,
      }}
    >
      {children}
    </StyledContainer>
  );
};

// Re-export standardized grid system
export {
  ResponsiveGrid,
  HealthcareGrid,
  HealthcareGridItem,
  GridPatterns,
  GridSpacing
} from './GridSystem';

// Responsive card component
interface ResponsiveCardProps {
  children: React.ReactNode;
  elevation?: number;
  padding?: boolean;
  fullHeight?: boolean;
  interactive?: boolean;
  className?: string;
}

export const ResponsiveCard: React.FC<ResponsiveCardProps> = ({
  children,
  elevation: _elevation = 1,
  padding = true,
  fullHeight = false,
  interactive = false,
  className,
}) => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));

  return (
    <Box
      {...(className && { className })}
      sx={{
        backgroundColor: 'background.paper',
        borderRadius: 2,
        overflow: 'hidden',
        height: fullHeight ? '100%' : 'auto',
        p: padding ? (isMobile ? 2 : 3) : 0,
        cursor: interactive ? 'pointer' : 'default',
      }}
    >
      {children}
    </Box>
  );
};

// Responsive stack component for vertical layouts
interface ResponsiveStackProps {
  children: React.ReactNode;
  spacing?: number;
  direction?: 'column' | 'row';
  alignItems?: 'flex-start' | 'center' | 'flex-end' | 'stretch';
  justifyContent?: 'flex-start' | 'center' | 'flex-end' | 'space-between' | 'space-around';
  responsive?: boolean;
}

export const ResponsiveStack: React.FC<ResponsiveStackProps> = ({
  children,
  spacing = 2,
  direction = 'column',
  alignItems = 'stretch',
  justifyContent = 'flex-start',
  responsive = true,
}) => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));

  return (
    <Box
      sx={{
        display: 'flex',
        flexDirection: responsive && isMobile ? 'column' : direction,
        gap: spacing,
        alignItems,
        justifyContent,
        width: '100%',
      }}
    >
      {children}
    </Box>
  );
};

// Responsive section component with semantic HTML
interface ResponsiveSectionProps {
  children: React.ReactNode;
  title?: string;
  subtitle?: string;
  padding?: boolean;
  background?: 'default' | 'paper' | 'primary' | 'secondary';
  id?: string;
  'aria-label'?: string;
}

export const ResponsiveSection: React.FC<ResponsiveSectionProps> = ({
  children,
  title,
  subtitle,
  padding = true,
  background = 'default',
  id,
  'aria-label': ariaLabel,
}) => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));

  const backgroundColors = {
    default: 'background.default',
    paper: 'background.paper',
    primary: 'primary.main',
    secondary: 'secondary.main',
  };

  return (
    <Box
      component="section"
      id={id}
      aria-label={ariaLabel || title}
      sx={{
        backgroundColor: backgroundColors[background],
        ...(padding && {
          py: isMobile ? 3 : 4,
          px: isMobile ? 2 : 3,
        }),
        ...(background === 'primary' || background === 'secondary') && {
          color: 'white',
        },
      }}
    >
      {title && (
        <Box sx={{ mb: subtitle ? 1 : 3 }}>
          <Box
            component="h2"
            sx={{
              typography: isMobile ? 'h5' : 'h4',
              fontWeight: 600,
              margin: 0,
              color: 'inherit',
            }}
          >
            {title}
          </Box>
          {subtitle && (
            <Box
              component="p"
              sx={{
                typography: 'body1',
                color: 'text.secondary',
                margin: 0,
                mt: 1,
                mb: 3,
              }}
            >
              {subtitle}
            </Box>
          )}
        </Box>
      )}
      {children}
    </Box>
  );
};

export default ResponsiveContainer;
